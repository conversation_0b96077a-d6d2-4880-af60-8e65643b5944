<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CountrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        // Path to your CSV file
        $csvFile = base_path('database/seeders/csv/countries.csv');

        // Open the file
        $file = fopen($csvFile, 'r');
        // dd($file);

        // Read the file line by line & collect array for insert
        $firstLine = true;
        $insertData = [];
        while (($data = fgetcsv($file, 1000, ",")) !== FALSE) {
            if (!$firstLine) {
                $whereArr = [
                    'name'          => $data[0],
                ];
                $exists = DB::table('counties')->where($whereArr)->exists();
                if(!$exists){
                    //$insertData[] = $whereArr;
                    $insertData[] = [
                        'name'          => $data[0],
                    ];
                }else{
                    DB::table('counties')->where($whereArr)->update(['name' => $data[0]]);
                }
            }
            $firstLine = false;
        }

        // Insert permissions
        if ($insertData) {
            DB::table('counties')->insert($insertData);
            echo 'Countries added '. count($insertData) .'.';
        }else{
            echo "Already Upto date all countries.";
        }

        // Close the file
        fclose($file);
    }
}
