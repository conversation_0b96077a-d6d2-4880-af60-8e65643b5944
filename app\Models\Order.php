<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Order extends Model
{
    use HasFactory, SoftDeletes, ActivityTrait;

    protected $table = 'orders';

    protected static $logName = 'Order';

    public function getLogDescription(string $event): string
    {
        return "Order <strong>{$this->order_number}</strong> has been {$event} by";
    }

    protected static $logAttributes = [
        'order_number', 'quotation_id', 'lead_id', 'selected_qty_1', 'selected_qty_2', 'production_stage',
        'selected_qty_3', 'selected_qty_4', 'price_qty_1', 'price_qty_2',
        'price_qty_3', 'price_qty_4', 'notes', 'status', 'is_confirmed',
        'confirmed_at', 'tracking_number', 'expected_delivery', 'actual_delivery', 'total_amount'
    ];

    protected $fillable = [
        'order_number', 'quotation_id', 'lead_id', 'selected_qty_1', 'selected_qty_2', 'production_stage',
        'selected_qty_3', 'selected_qty_4', 'price_qty_1', 'price_qty_2',
        'price_qty_3', 'price_qty_4', 'notes', 'status', 'is_confirmed',
        'confirmed_at', 'tracking_number', 'expected_delivery', 'actual_delivery',
        'total_amount', 'created_by', 'updated_by'
    ];

    protected $casts = [
        'expected_delivery' => 'date',
        'actual_delivery' => 'date',
        'confirmed_at' => 'datetime',
        'is_confirmed' => 'boolean',
        'price_qty_1' => 'decimal:2',
        'price_qty_2' => 'decimal:2',
        'price_qty_3' => 'decimal:2',
        'price_qty_4' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    public function lead()
    {
        return $this->belongsTo(Lead::class);
    }

    public function quotation()
    {
        return $this->belongsTo(Quotation::class);
    }

    // Access county through lead relationship
    public function county()
    {
        return $this->hasOneThrough(County::class, Lead::class, 'id', 'id', 'lead_id', 'county_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function documents()
    {
        return $this->hasMany(Document::class, 'entity_id')
            ->where('entity_type', 'order');
    }

    // Calculate total amount based on selected quantities and prices
    public function calculateTotal()
    {
        $total = 0;

        if (!empty($this->selected_qty_1) && !empty($this->price_qty_1)) {
            $total += floatval($this->selected_qty_1) * floatval($this->price_qty_1);
        }
        if (!empty($this->selected_qty_2) && !empty($this->price_qty_2)) {
            $total += floatval($this->selected_qty_2) * floatval($this->price_qty_2);
        }
        if (!empty($this->selected_qty_3) && !empty($this->price_qty_3)) {
            $total += floatval($this->selected_qty_3) * floatval($this->price_qty_3);
        }
        if (!empty($this->selected_qty_4) && !empty($this->price_qty_4)) {
            $total += floatval($this->selected_qty_4) * floatval($this->price_qty_4);
        }

        return $total;
    }

    // Auto-update total when saving
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($order) {
            $order->total_amount = $order->calculateTotal();
        });
    }
}
