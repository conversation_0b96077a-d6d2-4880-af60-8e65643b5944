<template>
    <div class="relative">
        <!-- Notification Bell Button -->
        <button @click="toggleDropdown"
            class="relative p-2 mt-1 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-full">

            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                 stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round"
                      d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"/>
            </svg>

            <!-- Notification Badge -->
            <span v-if="unreadCount > 0"
                  class="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
                {{ unreadCount > 99 ? '99+' : unreadCount }}
            </span>
        </button>

        <!-- Dropdown Menu -->
        <div v-if="showDropdown"
             class="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
             @click.stop>
            <!-- Header -->
            <div class="px-4 py-3 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-sm font-semibold text-gray-900">Notifications</h3>
                    <div class="flex space-x-2">
                        <button @click="markAllAsRead"
                                class="text-xs text-blue-600 hover:text-blue-800">
                            Mark all read
                        </button>
                        <Link :href="route('notifications.index')"
                              class="text-xs text-blue-600 hover:text-blue-800">
                            View all
                        </Link>
                    </div>
                </div>
            </div>

            <!-- Notifications List -->
            <div class="max-h-96 overflow-y-auto">
                <div v-if="notifications.length === 0" class="px-4 py-6 text-center text-gray-500">
                    <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM9 7H4l5-5v5zm6 10V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2h6a2 2 0 002-2z"></path>
                    </svg>
                    <p class="text-sm">No new notifications</p>
                </div>

                <div v-for="notification in notifications" :key="notification.id"
                     class="px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                     :class="{ 'bg-blue-50': !notification.is_read }"
                     @click="handleNotificationClick(notification)">
                    <div class="flex items-start space-x-3">
                        <!-- Icon -->
                        <div class="flex-shrink-0 mt-1">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center"
                                 :class="getNotificationIconBg(notification.type)">
                                <span class="text-sm">{{ getNotificationIcon(notification.type) }}</span>
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <p class="text-sm font-medium text-gray-900 truncate">
                                    {{ notification.title }}
                                </p>
                                <div class="flex items-center space-x-1">
                                    <span v-if="notification.priority === 'urgent'"
                                          class="inline-block w-2 h-2 bg-red-500 rounded-full"></span>
                                    <span v-else-if="notification.priority === 'high'"
                                          class="inline-block w-2 h-2 bg-orange-500 rounded-full"></span>
                                    <span v-if="!notification.is_read"
                                          class="inline-block w-2 h-2 bg-blue-500 rounded-full"></span>
                                </div>
                            </div>
                            <p class="text-xs text-gray-600 mt-1 line-clamp-2">
                                {{ notification.message }}
                            </p>
                            <p class="text-xs text-gray-500 mt-1">
                                {{ formatTimeAgo(notification.created_at) }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="px-4 py-3 border-t border-gray-200 bg-gray-50">
                <Link :href="route('notifications.index')"
                      class="block w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium">
                    View All Notifications
                </Link>
            </div>
        </div>

        <!-- Backdrop -->
        <div v-if="showDropdown"
             class="fixed inset-0 z-40"
             @click="showDropdown = false"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import axios from 'axios'; // ✅ Import axios

const showDropdown = ref(false)
const notifications = ref([])
const unreadCount = ref(0)

const toggleDropdown = async () => {
    showDropdown.value = !showDropdown.value
    if (showDropdown.value) {
        await fetchNotifications()
    }
}

const fetchNotifications = async () => {
    try {
        const response = await fetch(route('notifications.recent'))
        const data = await response.json()
        notifications.value = data
    } catch (error) {
        console.error('Failed to fetch notifications:', error)
    }
}

const fetchUnreadCount = async () => {
    try {
        const response = await fetch(route('notifications.unread-count'))
        const data = await response.json()
        unreadCount.value = data.count
    } catch (error) {
        console.error('Failed to fetch unread count:', error)
    }
}

const markAllAsRead = async () => {
    try {
        await axios.post('/notifications/read-all');
        notifications.value.forEach(n => {
            n.is_read = true
            n.read_at = new Date().toISOString()
        })
        unreadCount.value = 0
    } catch (error) {
        console.error('Failed to mark all as read:', error);
    }
};

const handleNotificationClick = async (notification) => {
    const id = notification.id;
    try {
        await axios.post(`/notifications/${id}/read`);
        const notification = notifications.value.find(n => n.id === notification.id);
        if (notification){
            notification.is_read = true
            notification.read_at = new Date().toISOString()
        }
        unreadCount.value = Math.max(0, unreadCount.value - 1)
    } catch (error) {
        console.error('Failed to mark notification as read:', error);
    }

    // Navigate to action URL if available
    if (notification.action_data && notification.action_data.url) {
        showDropdown.value = false
        window.location.href = notification.action_data.url
    }
}

const formatTimeAgo = (date) => {
    const now = new Date()
    const notificationDate = new Date(date)
    const diffInSeconds = Math.floor((now - notificationDate) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    return `${Math.floor(diffInSeconds / 86400)}d ago`
}

const getNotificationIcon = (type) => {
    const icons = {
        task_reminder: '⏰',
        task_overdue: '🚨',
        lead_update: '👤',
        quotation_update: '💰',
        order_update: '📦',
        system: '⚙️',
        follow_up_due: '📞',
        call_scheduled: '📅'
    }
    return icons[type] || '🔔'
}

const getNotificationIconBg = (type) => {
    const colors = {
        task_reminder: 'bg-blue-100',
        task_overdue: 'bg-red-100',
        lead_update: 'bg-green-100',
        quotation_update: 'bg-purple-100',
        order_update: 'bg-orange-100',
        system: 'bg-gray-100',
        follow_up_due: 'bg-yellow-100',
        call_scheduled: 'bg-indigo-100'
    }
    return colors[type] || 'bg-gray-100'
}

// Auto-refresh unread count every 30 seconds
let refreshInterval

onMounted(() => {
    fetchUnreadCount()
    // refreshInterval = setInterval(fetchUnreadCount, 1000)
})

onUnmounted(() => {
    if (refreshInterval) {
        clearInterval(refreshInterval)
    }
})
</script>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
