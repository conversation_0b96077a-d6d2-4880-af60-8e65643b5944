<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\User;
use Illuminate\Validation\Rule;

class UserUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $userId = $this->input('id');
        return [
            'first_name'    => ['required','string','max:255'],
            'last_name'     => ['required','string','max:255'],
            'email'         => ['required','string','email','max:255', Rule::unique(User::class)->ignore($userId)],
            'role_id'       => ['required'],
            'contact_no'    => ['required','numeric', 'digits:10'],
            'dob'           => ['required'],
            'address'       => ['required']
        ];
    }
}
