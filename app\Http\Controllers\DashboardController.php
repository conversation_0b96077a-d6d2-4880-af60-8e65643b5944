<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProfileUpdateRequest;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use App\Models\Lead;
use App\Models\Task;
use App\Models\User;
use App\Models\Order;
use App\Models\Quotation;
use Illuminate\Support\Facades\DB;


class DashboardController extends Controller
{

    public function dashboard(Request $request)
    {
        $organizationId = $request->input('organization_id');
        $permissions = auth()->user()->getAllPermissions()->pluck('name');
        $permissions = json_encode($permissions);

        // Recent orders
        $recentOrders = Order::with(['county', 'creator', 'quotation'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Order statistics
        $orderStats = [
            'total' => Order::count(),
            'confirmed' => Order::where('status', 'confirmed')->count(),
            'under_production' => Order::where('status', 'under_production')->count(),
            'shipped' => Order::where('status', 'shipped')->count(),
            'delivered' => Order::where('status', 'delivered')->count(),
        ];

        // Country-specific revenue stats
        $countryRevenue = [
            'total' => [
                'uk' => Order::whereHas('lead.county', function($q) {
                    $q->where('name', 'like', '%United Kingdom%');
                })->sum('total_amount'),

                'us' => Order::whereHas('lead.county', function($q) {
                    $q->where('name', 'like', '%United States%');
                })->sum('total_amount'),

                'canada' => Order::whereHas('lead.county', function($q) {
                    $q->where('name', 'like', '%Canada%');
                })->sum('total_amount'),

                'australia' => Order::whereHas('lead.county', function($q) {
                    $q->where('name', 'like', '%Australia%');
                })->sum('total_amount'),
            ],
            'monthly' => [
                'uk' => Order::whereHas('lead.county', function($q) {
                    $q->where('name', 'like', '%United Kingdom%');
                })->where('status', '!=', 'pending')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('total_amount'),

                'us' => Order::whereHas('lead.county', function($q) {
                    $q->where('name', 'like', '%United States%');
                })->where('status', '!=', 'pending')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('total_amount'),

                'canada' => Order::whereHas('lead.county', function($q) {
                    $q->where('name', 'like', '%Canada%');
                })->where('status', '!=', 'pending')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('total_amount'),

                'australia' => Order::whereHas('lead.county', function($q) {
                    $q->where('name', 'like', '%Australia%');
                })->where('status', '!=', 'pending')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('total_amount'),
            ]
        ];

        // Lead statistics
        $leadStats = [
            'total' => Lead::count(),
            'new' => Lead::where('status', 'new')->count(),
            'contacted' => Lead::where('status', 'contacted')->count(),
            'quotation' => Lead::where('status', 'quotation')->count(),
            'won' => Lead::where('status', 'won')->count(),
        ];

        // Quotation statistics
        $quotationStats = [
            'total' => Quotation::count(),
            'pending' => Quotation::where('status', 'pending')->count(),
            'quotation_ready' => Quotation::where('status', 'quotation_ready')->count(),
            'order_placed' => Quotation::where('status', 'order_placed')->count()
        ];

        $taskStats = [
            'total' => Task::count(),
            'pending' => Task::where('status', 'pending')->count(),
            'overdue' => Task::overdue()->count(),
            'due_today' => Task::dueToday()->count(),
        ];

        // Monthly trends (last 6 months)
        $monthlyTrends = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $monthlyTrends[] = [
                'month' => $date->format('M Y'),
                'leads' => Lead::whereMonth('created_at', $date->month)
                    ->whereYear('created_at', $date->year)->count(),
                'quotations' => Quotation::whereMonth('created_at', $date->month)
                    ->whereYear('created_at', $date->year)->count(),
                'orders' => Order::whereMonth('created_at', $date->month)
                    ->whereYear('created_at', $date->year)->count(),
                'revenue' => Order::where('status', '!=', 'pending')
                    ->whereMonth('created_at', $date->month)
                    ->whereYear('created_at', $date->year)
                    ->sum('total_amount'),
            ];
        }

        // Top performing agents
        $topAgents = User::withCount(['leads', 'quotations', 'orders'])
            ->withSum('orders', 'total_amount')
            ->orderBy('orders_sum_total_amount', 'desc')
            ->limit(5)
            ->get();

        // Recent activities (last 20)
        $recentActivities = collect();

        // Recent leads
        $recentLeads = Lead::with('creator')
            ->latest()
            ->limit(5)
            ->get()
            ->map(function($lead) {
                return [
                    'type' => 'lead',
                    'title' => "New lead: {$lead->client_name}",
                    'description' => "Lead {$lead->lead_number} created",
                    'user' => $lead->creator->first_name ?? 'System',
                    'created_at' => $lead->created_at,
                    'icon' => 'user-plus',
                    'color' => 'blue'
                ];
            });

        // Recent quotations
        $recentQuotations = Quotation::with('creator')
            ->latest()
            ->limit(5)
            ->get()
            ->map(function($quotation) {
                return [
                    'type' => 'quotation',
                    'title' => "Quotation sent: {$quotation->client_name}",
                    'description' => "Quotation {$quotation->quotation_number} created",
                    'user' => $quotation->creator->first_name ?? 'System',
                    'created_at' => $quotation->created_at,
                    'icon' => 'document-text',
                    'color' => 'green'
                ];
            });

        // Recent orders
        $recentOrderActivities = Order::with('creator')
            ->latest()
            ->limit(5)
            ->get()
            ->map(function($order) {
                return [
                    'type' => 'order',
                    'title' => "Order created: {$order->client_name}",
                    'description' => "Order {$order->order_number} - {$order->status}",
                    'user' => $order->creator->first_name ?? 'System',
                    'created_at' => $order->created_at,
                    'icon' => 'shopping-cart',
                    'color' => 'purple'
                ];
            });

        $recentActivities = $recentLeads
            ->concat($recentQuotations)
            ->concat($recentOrderActivities)
            ->sortByDesc('created_at')
            ->take(15)
            ->values();

        return Inertia::render('Dashboard', [
            'permissions' => $permissions,
            'recentOrders' => $recentOrders,
            'orderStats' => $orderStats,
            'taskStats' => $taskStats,
            'countryRevenue' => $countryRevenue,
            'leadStats' => $leadStats,
            'quotationStats' => $quotationStats,
            'monthlyTrends' => $monthlyTrends,
            'topAgents' => $topAgents,
            'recentActivities' => $recentActivities,
        ]);
    }
}
