<template>
    <div class="comments-card">
        <!-- Comments Summary -->
        <div  v-if="comments.length != 0" class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                <span class="text-sm font-medium text-gray-900">
                    {{ comments.length }} {{ comments.length === 1 ? 'Comment' : 'Comments' }}
                </span>
            </div>
            <button @click="showComments = !showComments"
                    class="text-xs text-blue-600 font-semibold hover:text-blue-800">
                {{ showComments ? 'Hide' : 'Show' }}
            </button>
        </div>

        <!-- Recent Comments Preview (when collapsed) -->
        <div v-if="!showComments && comments.length > 0" class="space-y-2 overflow-y-auto max-h-12">
            <div v-for="comment in comments" :key="comment.id"
                 class="text-xs p-2 rounded border-l-2"
                 :class="{
                     'border-red-400 bg-red-50': comment.type === 'lost_reason',
                     'border-gray-400 bg-gray-50': comment.type === 'general'
                 }">
                <div class="flex items-center justify-between mb-1">
                    <span class="font-medium text-gray-900">
                        {{ comment.user.first_name }} {{ comment.user.last_name }}
                    </span>
                    <span class="text-gray-500">{{ formatTimeAgo(comment.created_at) }}</span>
                </div>
                <p class="text-gray-600 line-clamp-2">{{ comment.comment }}</p>
            </div>
        </div>

        <!-- Full Comments List (when expanded) -->
        <div v-if="showComments" class="space-y-3  overflow-x-auto max-h-52">

            <!-- Add Comment Button -->
            <button @click="$emit('add-comment')"
                    class="w-full py-2 px-3 text-xs text-blue-600 border font-semibold border-blue-200 rounded-md hover:bg-blue-50 transition-colors">
                + Add Comment
            </button>
            <div v-for="comment in comments" :key="comment.id"
                 class="text-xs py-2 px-3 rounded-lg border"
                 :class="{
                     'border-red-200 bg-red-50': comment.type === 'lost_reason',
                     'border-gray-200 bg-gray-50': comment.type === 'general'
                 }">
                <div class="flex items-center justify-between mb-1">
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                            <span class="text-xs font-medium text-blue-600">
                                {{ comment.user.first_name.charAt(0) }}
                            </span>
                        </div>
                        <span class="font-medium text-gray-900">
                            {{ comment.user.first_name }} {{ comment.user.last_name }}
                        </span>
                        <span class="inline-flex px-2 py-1 rounded-full text-xs font-medium"
                              :class="getTypeColor(comment.type)">
                            {{ getTypeIcon(comment.type) }}
                        </span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-gray-500">{{ formatTimeAgo(comment.created_at) }}</span>
                        <!-- Edit/Delete Actions -->
                        <div v-if="canManageComment(comment)" class="flex space-x-1">
                            <button @click="startEdit(comment)"
                                    class="text-blue-600 hover:text-blue-800 text-xs">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5"><path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"></path></svg>
                            </button>
                            <button @click="deleteComment(comment.id)"
                                    class="text-red-600 hover:text-red-800 text-xs">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5"><path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"></path></svg>                            </button>
                        </div>
                    </div>
                </div>

                <!-- Comment Content -->
                <div v-if="editingComment?.id === comment.id" class="mt-2">
                    <textarea v-model="editForm.comment"
                              class="w-full p-2 border border-gray-300 rounded-md text-xs"
                              rows="2"></textarea>
                    <div class="flex space-x-2 mt-2">
                        <button @click="updateComment"
                                class="px-2 py-1 bg-indigo-600 text-white text-xs rounded-md hover:bg-indigo-700">
                            Save
                        </button>
                        <button @click="cancelEdit"
                                class="px-2 py-1 bg-gray-300 text-gray-700 text-xs rounded-md hover:bg-gray-400">
                            Cancel
                        </button>
                    </div>
                </div>
                <div v-else class="flex items-center justify-between">
                    <p class="text-gray-700 whitespace-pre-wrap text-xs">{{ comment.comment }}</p>

                    <!-- Edit indicator -->
                    <div v-if="comment.is_edited" class="mt-1 text-xs text-orange-600">
                        (edited {{ formatTimeAgo(comment.edited_at) }})
                    </div>
                </div>
            </div>

        </div>

        <!-- Empty State -->
        <div v-if="comments.length === 0" class="text-center py-4 text-gray-500">
            <svg class="mx-auto h-6 w-6 text-gray-400 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            <p class="text-xs">No comments yet</p>
            <button @click="$emit('add-comment')"
                    class="mt-2 text-xs font-semibold text-blue-600 hover:text-blue-800">
                Add first comment
            </button>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { router } from '@inertiajs/vue3'

const props = defineProps({
    comments: {
        type: Array,
        default: () => []
    },
    currentUserId: {
        type: Number,
        required: true
    },
    isAdmin: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['add-comment', 'comment-updated', 'comment-deleted'])

const showComments = ref(false)
const editingComment = ref(null)
const editForm = ref({ comment: '' })

const recentComments = computed(() => {
    return props.comments.slice(0, 2) // Show only 2 most recent comments when collapsed
})

const formatTimeAgo = (date) => {
    const now = new Date()
    const commentDate = new Date(date)
    const diffInSeconds = Math.floor((now - commentDate) / 1000)

    if (diffInSeconds < 60) return 'just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return commentDate.toLocaleDateString()
}

const getTypeColor = (type) => {
    const colors = {
        'lost_reason': 'bg-red-100 text-red-800',
        'general': 'bg-gray-100 text-gray-800'
    }
    return colors[type] || 'bg-gray-100 text-gray-800'
}

const getTypeIcon = (type) => {
    const icons = {
        'lost_reason': '❌',
        'general': '💬'
    }
    return icons[type] || '💬'
}

const canManageComment = (comment) => {
    return comment.user_id === props.currentUserId || props.isAdmin
}

const startEdit = (comment) => {
    editingComment.value = comment
    editForm.value.comment = comment.comment
}

const cancelEdit = () => {
    editingComment.value = null
    editForm.value.comment = ''
}

const updateComment = async () => {
    if (!editForm.value.comment.trim()) return

    try {
        router.put(route('leads.update-comment',  editingComment.value.id), {
            comment: editForm.value.comment
        }, {
            preserveScroll: true,
            preserveState: true,
            onSuccess: () => {
                const urlParams = new URLSearchParams(window.location.search);
                const currentPage = urlParams.get('page') || 1;
                newComment.value = ''
                commentType.value = 'general'
                emit('comment-updated', '');
                // Reload with current filters and page preserved
                router.get(route('leads.index'), {
                    search: searchValue.value,
                    agent_id: agentId.value === '' ? null : agentId.value,
                    county_id: countyId.value === '' ? null : countyId.value,
                    page: currentPage
                }, {
                    preserveScroll: true,
                    preserveState: true,
                    only: ['data']
                });
            },
            onError: (errors) => {
                console.error("Update failed:", errors);
                alert("Failed to update status. Please try again.");
            }
        });
    } catch (error) {
        console.error('Error updating comment:', error)
        alert('Failed to update comment')
    }
}

const deleteComment = async (commentId) => {
    try {
        router.delete(route('leads.delete-comment', commentId), {
            preserveScroll: true,
            preserveState: true,
            onSuccess: () => {
                const urlParams = new URLSearchParams(window.location.search);
                const currentPage = urlParams.get('page') || 1;
                newComment.value = ''
                commentType.value = 'general'
                emit('comment-deleted', '')
                // Reload with current filters and page preserved
                router.get(route('leads.index'), {
                    search: searchValue.value,
                    agent_id: agentId.value === '' ? null : agentId.value,
                    county_id: countyId.value === '' ? null : countyId.value,
                    page: currentPage
                }, {
                    preserveScroll: true,
                    preserveState: true,
                    only: ['data']
                });
            },
            onError: (errors) => {
                console.error("Update failed:", errors);
                alert("Failed to update status. Please try again.");
            }
        });
    } catch (error) {
        console.error('Error deleting comment:', error)
        alert('Failed to delete comment')
    }
}
</script>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.comments-card {
    max-height: 300px;
    overflow-y: auto;
}
</style>
