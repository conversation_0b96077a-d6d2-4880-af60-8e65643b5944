<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import FileViewer from '@/Components/FileViewer.vue';
import Modal from '@/Components/Modal.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import SearchableDropdown from '@/Components/SearchableDropdownNew.vue';
import MultipleFileUpload from '@/Components/MultipleFileUpload.vue';
import SvgLink from '@/Components/ActionLink.vue';
import { Head , usePage } from '@inertiajs/vue3';
import { ref } from 'vue';
import { useForm } from 'laravel-precognition-vue-inertia';

const props = defineProps({
    data: {
        type: Object,
    },
    counties: {
        type: Array,
        required: true
    },
    filepath: {
        type: String,
        required: true
    }
});

const data = usePage().props.data;
const file = usePage().props.filepath.view;

const form = useForm('post', '/leads', {
    id: data.id,
    client_name: data.client_name,
    county_id:  data.county_id,
    dimensions: data.dimensions,
    open_size: data.open_size,
    box_style: data.box_style,
    stock: data.stock,
    lamination: data.lamination,
    printing: data.printing,
    add_ons: data.add_ons,
    qty_1: data.qty_1,
    qty_2: data.qty_2,
    qty_3: data.qty_3,
    qty_4: data.qty_4,
    notes: data.notes,
    document: ''
});

const submit = () => form.submit({
    preserveScroll: true,
    onSuccess: () => form.reset(),
});

const setCounty = (id, name) => {
    form.county_id = id;
};

const handleDocument = (file) => {
    form.document = file;
};

const documentDeleteModal = ref(false);
const selectedDocumentId = ref(null);

const openDeleteModal = (id) => {
    selectedDocumentId.value = id;
    documentDeleteModal.value = true;
};

const deleteDocument = () => {
    form.get(route('removedocument', {id: selectedDocumentId.value}), {
        onSuccess: () => {
            closeDocumentModal()
        }
    });
};

const documentPreviewModal = ref(false);
const selectedDocument = ref(null);
const modalMaxWidth = ref('custom');

const openPreviewModal = (name) => {
    selectedDocument.value = name;
    documentPreviewModal.value = true;
};

const closeDocumentPreviewModal = () => {
    documentPreviewModal.value = false;
};

const downloadDocument = (name) => {
    const documentURL = window.location.origin + file + name;
    const link = document.createElement('a');
    link.href = documentURL;
    link.setAttribute('download', name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

</script>

<template>
    <Head title="Leads" />
    <AdminLayout>
        <div class="animate-top">
            <div class="bg-white p-4 shadow sm:p-6 rounded-lg border">
                <h2 class="text-2xl font-semibold leading-7 text-gray-900">Add New Lead</h2>
                <form @submit.prevent="submit" class="">
                    <div class="border-b border-gray-900/10 pb-12">
                        <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                            <div class="sm:col-span-2">
                                <InputLabel for="client_name" value="Client Name *" />
                                <TextInput
                                    id="client_name"
                                    type="text"
                                    v-model="form.client_name"
                                    required
                                    @change="form.validate('client_name')"
                                />
                                <InputError v-if="form.invalid('client_name')" :message="form.errors.client_name" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="county_id" value="Country *" />
                                <div class="relative mt-2">
                                    <SearchableDropdown
                                        :options="counties"
                                        v-model="form.county_id"
                                        @onchange="setCounty"
                                        required
                                        placeholder="Select Country"
                                    />
                                </div>
                                <InputError v-if="form.invalid('county_id')" :message="form.errors.county_id" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="email" value="Email" />
                                <TextInput
                                    id="email"
                                    type="text"
                                    v-model="form.email"
                                    @change="form.validate('email')"
                                />
                                <InputError v-if="form.invalid('email')" :message="form.errors.email" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="number" value="Number" />
                                <TextInput
                                    id="number"
                                    type="text"
                                    v-model="form.number"
                                    @change="form.validate('number')"
                                />
                                <InputError v-if="form.invalid('number')" :message="form.errors.number" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="dimensions" value="Dimensions *" />
                                <TextInput
                                    id="dimensions"
                                    type="text"
                                    v-model="form.dimensions"
                                    required
                                    @change="form.validate('dimensions')"
                                />
                                <InputError v-if="form.invalid('dimensions')" :message="form.errors.dimensions" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="open_size" value="Open Size  *" />
                                <TextInput
                                    id="open_size"
                                    type="text"
                                    v-model="form.open_size"
                                    required
                                    @change="form.validate('open_size')"
                                />
                                <InputError v-if="form.invalid('open_size')" :message="form.errors.open_size" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="box_style" value="Box Style *" />
                                <TextInput
                                    id="box_style"
                                    type="text"
                                    v-model="form.box_style"
                                    required
                                    @change="form.validate('box_style')"
                                />
                                <InputError v-if="form.invalid('box_style')" :message="form.errors.box_style" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="stock" value="Stock *" />
                                <TextInput
                                    id="stock"
                                    type="text"
                                    v-model="form.stock"
                                    required
                                    @change="form.validate('stock')"
                                />
                                <InputError v-if="form.invalid('stock')" :message="form.errors.stock" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="lamination" value="Lamination *" />
                                <TextInput
                                    id="lamination"
                                    type="text"
                                    v-model="form.lamination"
                                    required
                                    @change="form.validate('lamination')"
                                />
                                <InputError v-if="form.invalid('lamination')" :message="form.errors.lamination" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="printing" value="Printing *" />
                                <TextInput
                                    id="printing"
                                    type="text"
                                    v-model="form.printing"
                                    required
                                    @change="form.validate('printing')"
                                />
                                <InputError v-if="form.invalid('printing')" :message="form.errors.printing" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="add_ons" value="Add ons" />
                                <TextInput
                                    id="add_ons"
                                    type="text"
                                    v-model="form.add_ons"
                                    @change="form.validate('add_ons')"
                                />
                                <InputError v-if="form.invalid('add_ons')" :message="form.errors.add_ons" />
                            </div>


                            <div class="sm:col-span-1">
                                <InputLabel for="qty_1" value="QTY 1 *" />
                                <TextInput
                                    id="qty_1"
                                    type="text"
                                    :numeric="true"
                                    v-model="form.qty_1"
                                    required
                                    @change="form.validate('qty_1')"
                                />
                                <InputError v-if="form.invalid('qty_1')" :message="form.errors.qty_1" />
                            </div>

                            <div class="sm:col-span-1">
                                <InputLabel for="qty_2" value="QTY 2" />
                                <TextInput
                                    id="qty_2"
                                    type="text"
                                    :numeric="true"
                                    v-model="form.qty_2"
                                    @change="form.validate('qty_2')"
                                />
                                <InputError v-if="form.invalid('qty_2')" :message="form.errors.qty_2" />
                            </div>

                            <div class="sm:col-span-1">
                                <InputLabel for="qty_3" value="QTY 3" />
                                <TextInput
                                    id="qty_3"
                                    type="text"
                                    :numeric="true"
                                    v-model="form.qty_3"
                                    @change="form.validate('qty_3')"
                                />
                                <InputError v-if="form.invalid('qty_3')" :message="form.errors.qty_3" />
                            </div>

                            <div class="sm:col-span-1">
                                <InputLabel for="qty_4" value="QTY 4" />
                                <TextInput
                                    id="qty_4"
                                    type="text"
                                    :numeric="true"
                                    v-model="form.qty_4"
                                    @change="form.validate('qty_4')"
                                />
                                <InputError v-if="form.invalid('qty_4')" :message="form.errors.qty_4" />
                            </div>


                            <div class="sm:col-span-2">
                                <InputLabel for="note" value="File Upload"/>
                                <MultipleFileUpload
                                    inputId="document"
                                    inputName="document"
                                    @files="handleDocument"
                                />
                            </div>

                            <div class="sm:col-span-6">
                                <InputLabel for="notes" value="Notes"/>
                                <TextArea
                                    id="notes"
                                    type="text"
                                    :rows="3"
                                    v-model="form.notes"
                                    autocomplete="notes"
                                    @change="form.validate('notes')"
                                />
                                <InputError class="" :message="form.errors.notes" />
                            </div>
                            <div class="sm:col-span-6 bg-white p-1 shadow sm:rounded-lg border" v-if="data.documents && (data.documents.length > 0)">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">
                                            UPLOADED FILES
                                        </th>
                                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">ACTION</th>
                                    </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-300 bg-white">
                                    <tr v-for="(files, index) in data.documents" :key="data.id" class="">
                                        <td class="whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6">
                                            {{ files.orignal_name }}
                                        </td>
                                        <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center">
                                            <button type="button" @click="openDeleteModal(files.id)">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"/>
                                                </svg>
                                            </button>
                                            <button type="button" @click="openPreviewModal(files.name)">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                                </svg>
                                            </button>
                                            <button type="button" @click="downloadDocument(files.name)">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"></path>
                                                </svg>
                                            </button>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="flex mt-6 items-center justify-between">
                        <div class="ml-auto flex items-center justify-end gap-x-6">
                            <SvgLink :href="route('leads.index')">
                                <template #svg>
                                    <button type="button" class="text-sm font-semibold leading-6 text-gray-900">
                                        Cancel
                                    </button>
                                </template>
                            </SvgLink>
                        <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
                        <Transition
                            enter-active-class="transition ease-in-out"
                            enter-from-class="opacity-0"
                            leave-active-class="transition ease-in-out"
                            leave-to-class="opacity-0"
                        >
                            <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                        </Transition>
                        </div>
                    </div>
                </form>
            </div>


        </div>

        <Modal :show="documentDeleteModal" @close="closeDocumentModal">
            <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete this document?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeDocumentModal"> Cancel</SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteDocument"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
        <Modal :show="documentPreviewModal" @close="closeDocumentPreviewModal" :maxWidth="modalMaxWidth">
            <div class="p-6">
                <FileViewer :fileUrl="file+ selectedDocument"/>
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="closeDocumentPreviewModal"> Cancel</SecondaryButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>
</template>
