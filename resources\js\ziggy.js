const Ziggy = {"url":"http:\/\/localhost","port":null,"defaults":{},"routes":{"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"ignition.healthCheck":{"uri":"_ignition\/health-check","methods":["GET","HEAD"]},"ignition.executeSolution":{"uri":"_ignition\/execute-solution","methods":["POST"]},"ignition.updateConfig":{"uri":"_ignition\/update-config","methods":["POST"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"profile.edit":{"uri":"profile","methods":["GET","HEAD"]},"profile.update":{"uri":"profile","methods":["PATCH"]},"profile.destroy":{"uri":"profile","methods":["DELETE"]},"users.index":{"uri":"users","methods":["GET","HEAD"]},"users.create":{"uri":"users\/create","methods":["GET","HEAD"]},"users.store":{"uri":"users","methods":["POST"]},"users.show":{"uri":"users\/{user}","methods":["GET","HEAD"]},"users.edit":{"uri":"users\/{user}\/edit","methods":["GET","HEAD"]},"users.update":{"uri":"users","methods":["PATCH"]},"users.destroy":{"uri":"users\/delete\/{id}","methods":["GET","HEAD"]},"users.activation":{"uri":"users\/activation","methods":["POST"]},"customers.index":{"uri":"customers","methods":["GET","HEAD"]},"customers.create":{"uri":"customers\/create","methods":["GET","HEAD"]},"customers.store":{"uri":"customers","methods":["POST"]},"customers.show":{"uri":"customers\/{customer}","methods":["GET","HEAD"]},"customers.edit":{"uri":"customers\/{customer}\/edit","methods":["GET","HEAD"]},"customers.update":{"uri":"customers","methods":["PATCH"]},"customers.destroy":{"uri":"customers\/delete\/{id}","methods":["GET","HEAD"]},"customers.activation":{"uri":"customers\/activation","methods":["POST"]},"companies.index":{"uri":"companies","methods":["GET","HEAD"]},"companies.create":{"uri":"companies\/create","methods":["GET","HEAD"]},"companies.store":{"uri":"companies","methods":["POST"]},"companies.show":{"uri":"companies\/{company}","methods":["GET","HEAD"]},"companies.edit":{"uri":"companies\/{company}\/edit","methods":["GET","HEAD"]},"companies.update":{"uri":"companies","methods":["PATCH"]},"companies.destroy":{"uri":"companies\/delete\/{id}","methods":["GET","HEAD"]},"companies.activation":{"uri":"companies\/activation","methods":["POST"]},"products.index":{"uri":"products","methods":["GET","HEAD"]},"products.create":{"uri":"products\/create","methods":["GET","HEAD"]},"products.store":{"uri":"products","methods":["POST"]},"products.show":{"uri":"products\/{id}","methods":["GET","HEAD"]},"products.edit":{"uri":"products\/{product}\/edit","methods":["GET","HEAD"]},"products.update":{"uri":"products","methods":["PATCH"]},"products.destroy":{"uri":"products\/delete\/{id}","methods":["GET","HEAD"]},"products.activation":{"uri":"products\/activation","methods":["POST"]},"companypo.index":{"uri":"companypo","methods":["GET","HEAD"]},"companypo.create":{"uri":"companypo\/create","methods":["GET","HEAD"]},"companypo.store":{"uri":"companypo","methods":["POST"]},"companypo.show":{"uri":"companypo\/{companypo}","methods":["GET","HEAD"]},"companypo.edit":{"uri":"companypo\/{companypo}\/edit","methods":["GET","HEAD"]},"companypo.update":{"uri":"companypo\/{companypo}","methods":["PUT","PATCH"]},"companypo.destroy":{"uri":"companypo\/delete\/{id}","methods":["GET","HEAD"]},"companypo.receivepo":{"uri":"receivepo\/{id}","methods":["GET","HEAD"]},"companypo.viewpo":{"uri":"viewpo\/{id}","methods":["GET","HEAD"]},"challan.index":{"uri":"challan","methods":["GET","HEAD"]},"challan.create":{"uri":"challan\/create","methods":["GET","HEAD"]},"challan.store":{"uri":"challan","methods":["POST"]},"challan.show":{"uri":"challan\/{challan}","methods":["GET","HEAD"]},"challan.edit":{"uri":"challan\/{challan}\/edit","methods":["GET","HEAD"]},"challan.update":{"uri":"challan\/{challan}","methods":["PUT","PATCH"]},"challan.destroy":{"uri":"challan\/delete\/{id}","methods":["GET","HEAD"]},"challan.view":{"uri":"viewchallan\/{id}","methods":["GET","HEAD"]},"challan.invoice":{"uri":"generate-challan\/{id}","methods":["GET","HEAD"]},"quotation.index":{"uri":"quotation","methods":["GET","HEAD"]},"quotation.create":{"uri":"quotation\/create","methods":["GET","HEAD"]},"quotation.store":{"uri":"quotation","methods":["POST"]},"quotation.show":{"uri":"quotation\/{quotation}","methods":["GET","HEAD"]},"quotation.edit":{"uri":"quotation\/{quotation}\/edit","methods":["GET","HEAD"]},"quotation.update":{"uri":"quotation\/{quotation}","methods":["PUT","PATCH"]},"quotation.destroy":{"uri":"quotation\/delete\/{id}","methods":["GET","HEAD"]},"quotation.view":{"uri":"viewquotation\/{id}","methods":["GET","HEAD"]},"quotation.convertorder":{"uri":"convertorder\/{id}","methods":["GET","HEAD"]},"orders.index":{"uri":"orders","methods":["GET","HEAD"]},"orders.create":{"uri":"orders\/create","methods":["GET","HEAD"]},"orders.store":{"uri":"orders","methods":["POST"]},"orders.show":{"uri":"orders\/{order}","methods":["GET","HEAD"]},"orders.edit":{"uri":"orders\/{order}\/edit","methods":["GET","HEAD"]},"orders.update":{"uri":"orders\/{order}","methods":["PUT","PATCH"]},"orders.destroy":{"uri":"orders\/delete\/{id}","methods":["GET","HEAD"]},"orders.view":{"uri":"vieworders\/{id}","methods":["GET","HEAD"]},"orders.deliver":{"uri":"orderdeliver\/{id}","methods":["GET","HEAD"]},"invoice.index":{"uri":"invoice","methods":["GET","HEAD"]},"invoice.create":{"uri":"invoice\/create","methods":["GET","HEAD"]},"invoice.store":{"uri":"invoice","methods":["POST"]},"invoice.show":{"uri":"invoice\/{invoice}","methods":["GET","HEAD"]},"invoice.edit":{"uri":"invoice\/{invoice}\/edit","methods":["GET","HEAD"]},"invoice.update":{"uri":"invoice\/{invoice}","methods":["PUT","PATCH"]},"invoice.destroy":{"uri":"invoice\/{invoice}","methods":["DELETE"]},"vision-info":{"uri":"vision-info","methods":["GET","HEAD"]},"manage-prefix":{"uri":"manage-prefix","methods":["GET","HEAD"]},"saveVisionInfo":{"uri":"savevision","methods":["POST"]},"savePrefixInfo":{"uri":"saveprefix","methods":["POST"]},"activityLogs":{"uri":"activity-logs","methods":["POST"]},"removedocument":{"uri":"removedocument\/{id}\/{name}","methods":["GET","HEAD"]},"removeproduct":{"uri":"removeproduct\/{id}\/{model}","methods":["GET","HEAD"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"password.update":{"uri":"password","methods":["PUT"]},"logout":{"uri":"logout","methods":["POST"]}}};

if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
    Object.assign(Ziggy.routes, window.Ziggy.routes);
}

export { Ziggy };
