<template>
    <div v-if="showWarningNotification && message" id="toast-success" aria-live="assertive" class="pointer-events-none fixed inset-0 flex items-end px-4 py-6 sm:items-start sm:p-6 z-40">
        <div class="flex w-full flex-col items-center space-y-4 sm:items-end">
            <div class="pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg bg-yellow-100 border border-yellow-400 shadow-lg ring-1 ring-black ring-opacity-5">
                <div class="p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-yellow-700" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 2L2 22h20L12 2zM12 16v-4M12 20v-2" />
                            </svg>
                        </div>
                        <div class="ml-3 w-0 flex-1 pt-0.5">
                            <p class="text-sm font-medium text-yellow-700">{{ message }}!</p>
                        </div>
                        <div class="ml-4 flex flex-shrink-0">
                            <button type="button" class="inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                                <span class="sr-only">Close</span>
                                <svg class="h-5 w-5 text-yellow-700 bg-yellow-100" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
    import { usePage } from "@inertiajs/vue3";
    import { defineProps , ref , onMounted } from 'vue';

    const props = defineProps({
        message: String,
    })

    const showWarningNotification = ref(false);

    const handleFlash = ()=>{
        showWarningNotification.value = false;
        usePage().props.flash.warning = '';
    }

    onMounted( () => {
        showWarningNotification.value = true;
        setTimeout(()=> handleFlash() , 2000)
    })
</script>
