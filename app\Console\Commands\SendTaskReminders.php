<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Task;
use App\Services\NotificationService;
use Carbon\Carbon;

class SendTaskReminders extends Command
{
    protected $signature = 'tasks:send-reminders';
    protected $description = 'Send reminder notifications for upcoming and overdue tasks';

    public function handle()
    {
        $this->info('Checking for task reminders...');

        // Get tasks that need reminders (reminder_date is now or past)
        $reminderTasks = Task::whereNotNull('reminder_date')
            ->where('reminder_date', '<=', now())
            ->whereIn('status', ['pending', 'in_progress'])
            ->whereDoesntHave('notifications', function ($query) {
                $query->where('type', 'task_reminder')
                      ->where('created_at', '>=', now()->subHours(1)); // Don't spam reminders
            })
            ->get();

        foreach ($reminderTasks as $task) {
            NotificationService::createTaskReminder($task);
            $this->info("Reminder sent for task: {$task->title}");
        }

        // Get overdue tasks
        $overdueTasks = Task::where('due_date', '<', now())
            ->whereIn('status', ['pending', 'in_progress'])
            ->whereDoesntHave('notifications', function ($query) {
                $query->where('type', 'task_overdue')
                      ->where('created_at', '>=', now()->subHours(6)); // Send overdue reminder every 6 hours
            })
            ->get();

        foreach ($overdueTasks as $task) {
            NotificationService::createTaskOverdue($task);
            $this->info("Overdue notification sent for task: {$task->title}");
        }

        // Get tasks due today (for morning reminder)
        if (now()->hour === 9) { // Send at 9 AM
            $todayTasks = Task::whereDate('due_date', today())
                ->whereIn('status', ['pending', 'in_progress'])
                ->whereDoesntHave('notifications', function ($query) {
                    $query->where('type', 'task_reminder')
                          ->whereDate('created_at', today());
                })
                ->get();

            foreach ($todayTasks as $task) {
                NotificationService::createTaskReminder($task);
                $this->info("Daily reminder sent for task: {$task->title}");
            }
        }

        $this->info('Task reminders completed.');
        return 0;
    }
}
