<template>
    <div class="lead-comments">
        <!-- Add Comment Form -->
        <div v-if="showAddForm" class="bg-white border border-gray-200 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Add Comment</h4>
            <form @submit.prevent="addComment">
                <!-- Comment Type Selection -->
                <div class="mb-3">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Comment Type</label>
                    <div class="flex space-x-4">
                        <label class="flex items-center">
                            <input type="radio" v-model="commentType" value="general"
                                   class="mr-2 text-blue-600 focus:ring-blue-500">
                            <span class="text-sm">General Comment</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" v-model="commentType" value="lost_reason"
                                   class="mr-2 text-red-600 focus:ring-red-500">
                            <span class="text-sm">Lost Reason</span>
                        </label>
                    </div>
                </div>

                <!-- Comment Text -->
                <textarea v-model="newComment"
                          :placeholder="commentType === 'lost_reason' ? 'Explain why this lead was lost...' : 'Write your comment here...'"
                          class="w-full p-3 border border-gray-300 rounded-md text-sm"
                          rows="3"
                          required></textarea>

                <!-- Lost Reason Helper Text -->
                <div v-if="commentType === 'lost_reason'" class="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
                    <p class="text-xs text-red-700">
                        <strong>Note:</strong> Enter the reason why this lead was lost.
                    </p>
                </div>

                <div class="flex justify-between items-center mt-3">
                    <div class="text-xs text-gray-500">
                        {{ newComment.length }}/1000 characters
                    </div>
                    <div class="flex space-x-2">
                        <button type="button" @click="$emit('close')"
                                class="px-4 py-2 text-sm text-gray-600 hover:text-gray-800">
                            Cancel
                        </button>
                        <button type="submit"
                                :disabled="!newComment.trim() || newComment.length > 1000"
                                class="px-4 py-2 text-white text-sm rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
                                :class="commentType === 'lost_reason' ? 'bg-red-600 hover:bg-red-700' : 'bg-indigo-600 hover:bg-indigo-700'">
                            {{ commentType === 'lost_reason' ? 'Add Lost Reason' : 'Add Comment' }}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { router } from '@inertiajs/vue3'

const props = defineProps({
    leadId: {
        type: Number,
        required: true
    },
    comments: {
        type: Array,
        default: () => []
    },
    showAddForm: {
        type: Boolean,
        default: true
    },
    currentUserId: {
        type: Number,
        required: true
    },
    isAdmin: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['commentAdded', 'commentUpdated', 'commentDeleted', 'close'])

const newComment = ref('')
const commentType = ref('general')
const editingComment = ref(null)
const editForm = ref({ comment: '' })

const formatDateTime = (date) => {
    return new Date(date).toLocaleString()
}

const addComment = async () => {
    if (!newComment.value.trim()) return

    try {
        router.post(route('leads.add-comment',  props.leadId), {
            comment: newComment.value,
            type: commentType.value
        }, {
            preserveScroll: true,
            preserveState: true,
            onSuccess: () => {
                const urlParams = new URLSearchParams(window.location.search);
                const currentPage = urlParams.get('page') || 1;
                newComment.value = ''
                commentType.value = 'general'
                emit('close')
                emit('commentAdded', '');
                // Reload with current filters and page preserved
                router.get(route('leads.index'), {
                    search: searchValue.value,
                    agent_id: agentId.value === '' ? null : agentId.value,
                    county_id: countyId.value === '' ? null : countyId.value,
                    page: currentPage
                }, {
                    preserveScroll: true,
                    preserveState: true,
                    only: ['data']
                });
            },
            onError: (errors) => {
                console.error("Update failed:", errors);
                alert("Failed to update status. Please try again.");
            }
        });
    } catch (error) {
        console.error('Error adding comment:', error)
        alert('Failed to add comment')
    }
}

</script>

<style scoped>
.lead-comments {
    max-height: 400px;
    overflow-y: auto;
}
</style>
