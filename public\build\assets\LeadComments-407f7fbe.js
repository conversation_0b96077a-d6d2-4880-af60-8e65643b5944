import{r as x,c as B,b as n,d as l,h as e,t as r,j as _,F as $,k as S,n as w,i as C,v as L,O as g,p as M,m as A,I,s as T,l as N}from"./app-1bd09312.js";import{_ as P}from"./_plugin-vue_export-helper-c27b6911.js";const b=o=>(M("data-v-1496b86e"),o=o(),A(),o),D={class:"comments-card"},R={key:0,class:"flex items-center justify-between mb-3"},q={class:"flex items-center space-x-2"},z=b(()=>e("svg",{class:"w-4 h-4 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})],-1)),H={class:"text-sm font-medium text-gray-900"},O={key:1,class:"space-y-2 overflow-y-auto max-h-12"},G={class:"flex items-center justify-between mb-1"},W={class:"font-medium text-gray-900"},J={class:"text-gray-500"},K={class:"text-gray-600 line-clamp-2"},Q={key:2,class:"space-y-3 overflow-x-auto max-h-52"},X={class:"flex items-center justify-between mb-1"},Y={class:"flex items-center space-x-2"},Z={class:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center"},ee={class:"text-xs font-medium text-blue-600"},te={class:"font-medium text-gray-900"},se={class:"flex items-center space-x-2"},oe={class:"text-gray-500"},ae={key:0,class:"flex space-x-1"},re=["onClick"],ne=b(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),le=[ne],de=["onClick"],ce=b(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),ie=[ce],ue={key:0,class:"mt-2"},me=["onUpdate:modelValue"],pe={key:1,class:"flex items-center justify-between"},ve={class:"text-gray-700 whitespace-pre-wrap text-xs"},_e={key:0,class:"mt-1 text-xs text-orange-600"},xe={key:3,class:"text-center py-4 text-gray-500"},ge=b(()=>e("svg",{class:"mx-auto h-6 w-6 text-gray-400 mb-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})],-1)),he=b(()=>e("p",{class:"text-xs"},"No comments yet",-1)),ye={__name:"CommentsCard",props:{comments:{type:Array,default:()=>[]},currentUserId:{type:Number,required:!0},isAdmin:{type:Boolean,default:!1}},emits:["add-comment","comment-updated","comment-deleted"],setup(o,{emit:h}){const y=o,c=x(!1),d=x(null),p=x({comment:""});B(()=>y.comments.slice(0,2));const u=s=>{const a=new Date,t=new Date(s),m=Math.floor((a-t)/1e3);return m<60?"just now":m<3600?`${Math.floor(m/60)}m ago`:m<86400?`${Math.floor(m/3600)}h ago`:m<604800?`${Math.floor(m/86400)}d ago`:t.toLocaleDateString()},i=s=>({lost_reason:"bg-red-100 text-red-800",general:"bg-gray-100 text-gray-800"})[s]||"bg-gray-100 text-gray-800",v=s=>({lost_reason:"❌",general:"💬"})[s]||"💬",U=s=>s.user_id===y.currentUserId||y.isAdmin,j=s=>{d.value=s,p.value.comment=s.comment},E=()=>{d.value=null,p.value.comment=""},F=async()=>{if(p.value.comment.trim())try{g.put(route("leads.update-comment",d.value.id),{comment:p.value.comment},{preserveScroll:!0,preserveState:!0,onSuccess:()=>{const a=new URLSearchParams(window.location.search).get("page")||1;newComment.value="",commentType.value="general",h("comment-updated",""),g.get(route("leads.index"),{search:searchValue.value,agent_id:agentId.value===""?null:agentId.value,county_id:countyId.value===""?null:countyId.value,page:a},{preserveScroll:!0,preserveState:!0,only:["data"]})},onError:s=>{console.error("Update failed:",s),alert("Failed to update status. Please try again.")}})}catch(s){console.error("Error updating comment:",s),alert("Failed to update comment")}},V=async s=>{try{g.delete(route("leads.delete-comment",s),{preserveScroll:!0,preserveState:!0,onSuccess:()=>{const t=new URLSearchParams(window.location.search).get("page")||1;newComment.value="",commentType.value="general",h("comment-deleted",""),g.get(route("leads.index"),{search:searchValue.value,agent_id:agentId.value===""?null:agentId.value,county_id:countyId.value===""?null:countyId.value,page:t},{preserveScroll:!0,preserveState:!0,only:["data"]})},onError:a=>{console.error("Update failed:",a),alert("Failed to update status. Please try again.")}})}catch(a){console.error("Error deleting comment:",a),alert("Failed to delete comment")}};return(s,a)=>(n(),l("div",D,[o.comments.length!=0?(n(),l("div",R,[e("div",q,[z,e("span",H,r(o.comments.length)+" "+r(o.comments.length===1?"Comment":"Comments"),1)]),e("button",{onClick:a[0]||(a[0]=t=>c.value=!c.value),class:"text-xs text-blue-600 font-semibold hover:text-blue-800"},r(c.value?"Hide":"Show"),1)])):_("",!0),!c.value&&o.comments.length>0?(n(),l("div",O,[(n(!0),l($,null,S(o.comments,t=>(n(),l("div",{key:t.id,class:w(["text-xs p-2 rounded border-l-2",{"border-red-400 bg-red-50":t.type==="lost_reason","border-gray-400 bg-gray-50":t.type==="general"}])},[e("div",G,[e("span",W,r(t.user.first_name)+" "+r(t.user.last_name),1),e("span",J,r(u(t.created_at)),1)]),e("p",K,r(t.comment),1)],2))),128))])):_("",!0),c.value?(n(),l("div",Q,[e("button",{onClick:a[1]||(a[1]=t=>s.$emit("add-comment")),class:"w-full py-2 px-3 text-xs text-blue-600 border font-semibold border-blue-200 rounded-md hover:bg-blue-50 transition-colors"}," + Add Comment "),(n(!0),l($,null,S(o.comments,t=>{var m;return n(),l("div",{key:t.id,class:w(["text-xs py-2 px-3 rounded-lg border",{"border-red-200 bg-red-50":t.type==="lost_reason","border-gray-200 bg-gray-50":t.type==="general"}])},[e("div",X,[e("div",Y,[e("div",Z,[e("span",ee,r(t.user.first_name.charAt(0)),1)]),e("span",te,r(t.user.first_name)+" "+r(t.user.last_name),1),e("span",{class:w(["inline-flex px-2 py-1 rounded-full text-xs font-medium",i(t.type)])},r(v(t.type)),3)]),e("div",se,[e("span",oe,r(u(t.created_at)),1),U(t)?(n(),l("div",ae,[e("button",{onClick:k=>j(t),class:"text-blue-600 hover:text-blue-800 text-xs"},le,8,re),e("button",{onClick:k=>V(t.id),class:"text-red-600 hover:text-red-800 text-xs"},ie,8,de)])):_("",!0)])]),((m=d.value)==null?void 0:m.id)===t.id?(n(),l("div",ue,[C(e("textarea",{"onUpdate:modelValue":k=>p.value.comment=k,class:"w-full p-2 border border-gray-300 rounded-md text-xs",rows:"2"},null,8,me),[[L,p.value.comment]]),e("div",{class:"flex space-x-2 mt-2"},[e("button",{onClick:F,class:"px-2 py-1 bg-indigo-600 text-white text-xs rounded-md hover:bg-indigo-700"}," Save "),e("button",{onClick:E,class:"px-2 py-1 bg-gray-300 text-gray-700 text-xs rounded-md hover:bg-gray-400"}," Cancel ")])])):(n(),l("div",pe,[e("p",ve,r(t.comment),1),t.is_edited?(n(),l("div",_e," (edited "+r(u(t.edited_at))+") ",1)):_("",!0)]))],2)}),128))])):_("",!0),o.comments.length===0?(n(),l("div",xe,[ge,he,e("button",{onClick:a[2]||(a[2]=t=>s.$emit("add-comment")),class:"mt-2 text-xs font-semibold text-blue-600 hover:text-blue-800"}," Add first comment ")])):_("",!0)]))}},qe=P(ye,[["__scopeId","data-v-1496b86e"]]);const f=o=>(M("data-v-539d8e67"),o=o(),A(),o),be={class:"lead-comments"},fe={key:0,class:"bg-white border border-gray-200 rounded-lg p-4"},we=f(()=>e("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Add Comment",-1)),Ce=["onSubmit"],ke={class:"mb-3"},$e=f(()=>e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Comment Type",-1)),Se={class:"flex space-x-4"},Ie={class:"flex items-center"},Le=f(()=>e("span",{class:"text-sm"},"General Comment",-1)),Me={class:"flex items-center"},Ae=f(()=>e("span",{class:"text-sm"},"Lost Reason",-1)),Pe=["placeholder"],Ue={key:0,class:"mt-2 p-2 bg-red-50 border border-red-200 rounded-md"},je=f(()=>e("p",{class:"text-xs text-red-700"},[e("strong",null,"Note:"),N(" Enter the reason why this lead was lost. ")],-1)),Ee=[je],Fe={class:"flex justify-between items-center mt-3"},Ve={class:"text-xs text-gray-500"},Be={class:"flex space-x-2"},Te=["disabled"],Ne={__name:"LeadComments",props:{leadId:{type:Number,required:!0},comments:{type:Array,default:()=>[]},showAddForm:{type:Boolean,default:!0},currentUserId:{type:Number,required:!0},isAdmin:{type:Boolean,default:!1}},emits:["commentAdded","commentUpdated","commentDeleted","close"],setup(o,{emit:h}){const y=o,c=x(""),d=x("general");x(null),x({comment:""});const p=async()=>{if(c.value.trim())try{g.post(route("leads.add-comment",y.leadId),{comment:c.value,type:d.value},{preserveScroll:!0,preserveState:!0,onSuccess:()=>{const i=new URLSearchParams(window.location.search).get("page")||1;c.value="",d.value="general",h("close"),h("commentAdded",""),g.get(route("leads.index"),{search:searchValue.value,agent_id:agentId.value===""?null:agentId.value,county_id:countyId.value===""?null:countyId.value,page:i},{preserveScroll:!0,preserveState:!0,only:["data"]})},onError:u=>{console.error("Update failed:",u),alert("Failed to update status. Please try again.")}})}catch(u){console.error("Error adding comment:",u),alert("Failed to add comment")}};return(u,i)=>(n(),l("div",be,[o.showAddForm?(n(),l("div",fe,[we,e("form",{onSubmit:T(p,["prevent"])},[e("div",ke,[$e,e("div",Se,[e("label",Ie,[C(e("input",{type:"radio","onUpdate:modelValue":i[0]||(i[0]=v=>d.value=v),value:"general",class:"mr-2 text-blue-600 focus:ring-blue-500"},null,512),[[I,d.value]]),Le]),e("label",Me,[C(e("input",{type:"radio","onUpdate:modelValue":i[1]||(i[1]=v=>d.value=v),value:"lost_reason",class:"mr-2 text-red-600 focus:ring-red-500"},null,512),[[I,d.value]]),Ae])])]),C(e("textarea",{"onUpdate:modelValue":i[2]||(i[2]=v=>c.value=v),placeholder:d.value==="lost_reason"?"Explain why this lead was lost...":"Write your comment here...",class:"w-full p-3 border border-gray-300 rounded-md text-sm",rows:"3",required:""},null,8,Pe),[[L,c.value]]),d.value==="lost_reason"?(n(),l("div",Ue,Ee)):_("",!0),e("div",Fe,[e("div",Ve,r(c.value.length)+"/1000 characters ",1),e("div",Be,[e("button",{type:"button",onClick:i[3]||(i[3]=v=>u.$emit("close")),class:"px-4 py-2 text-sm text-gray-600 hover:text-gray-800"}," Cancel "),e("button",{type:"submit",disabled:!c.value.trim()||c.value.length>1e3,class:w(["px-4 py-2 text-white text-sm rounded-md disabled:opacity-50 disabled:cursor-not-allowed",d.value==="lost_reason"?"bg-red-600 hover:bg-red-700":"bg-indigo-600 hover:bg-indigo-700"])},r(d.value==="lost_reason"?"Add Lost Reason":"Add Comment"),11,Te)])])],40,Ce)])):_("",!0)]))}},ze=P(Ne,[["__scopeId","data-v-539d8e67"]]);export{qe as C,ze as L};
