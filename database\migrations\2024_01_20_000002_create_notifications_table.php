<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('message');

            // Notification types
            $table->enum('type', [
                'task_reminder', 'task_overdue', 'lead_update', 'quotation_update',
                'order_update', 'system', 'follow_up_due', 'call_scheduled'
            ]);

            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');

            // User relationship
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            // Related entity (polymorphic)
            $table->morphs('notifiable'); // notifiable_type, notifiable_id

            // Status tracking
            $table->boolean('is_read')->default(false);
            $table->datetime('read_at')->nullable();
            $table->datetime('scheduled_for')->nullable(); // For scheduled notifications

            // Action data
            $table->json('action_data')->nullable(); // URLs, buttons, etc.

            $table->timestamps();

            // Indexes
            $table->index(['user_id', 'is_read', 'created_at']);
            // $table->index(['notifiable_type', 'notifiable_id']);
            $table->index(['scheduled_for', 'is_read']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('notifications');
    }
};
