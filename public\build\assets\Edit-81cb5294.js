import{r as k,T as Z,w as Y,b as y,d as g,e as l,u as o,f as P,F as ee,Z as te,h as e,t as r,j as x,s as ae,l as se}from"./app-1bd09312.js";import{_ as le,a as de}from"./AdminLayout-0309e1ff.js";import{_ as C,a as v}from"./TextInput-6d9e32d1.js";import{_ as c}from"./InputLabel-9c4f245f.js";import{P as oe}from"./PrimaryButton-2e48b104.js";import{_ as ne}from"./TextArea-55151c24.js";import{_ as S}from"./Checkbox-b52dfb94.js";import{_ as ie}from"./SearchableDropdownNew-fa4bfabd.js";import"./_plugin-vue_export-helper-c27b6911.js";const re={class:"animate-top bg-white p-4 shadow sm:p-6 rounded-lg border"},ce={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6"},ue={class:"text-xl sm:text-2xl font-semibold leading-7 text-gray-900"},_e={class:"text-sm text-gray-600 mt-1"},me={class:"flex justify-start sm:justify-end"},ye={key:0,class:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"},ge={class:"mb-8 p-4 bg-gray-50 rounded-lg"},ve=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Order Summary",-1),xe={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},pe=e("p",{class:"text-sm font-semibold text-gray-900"},"Quotation",-1),qe={class:"text-sm text-gray-700"},fe=e("p",{class:"text-sm font-semibold text-gray-900"},"Lead",-1),be={class:"text-sm text-gray-700"},he=e("p",{class:"text-sm font-semibold text-gray-900"},"Total Amount",-1),ke={class:"text-sm font-semibold text-green-700"},we=e("p",{class:"text-sm font-semibold text-gray-900"},"Created By",-1),Ae={class:"text-sm text-gray-700"},Ve=["onSubmit"],Ce={class:"border-b border-gray-900/10 pb-12"},Qe={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Ue={class:"sm:col-span-12"},Ne=e("h3",{class:"text-lg font-semibold text-gray-900"},"Edit Order Quantities",-1),$e=e("p",{class:"text-sm text-gray-600"},'Current order quantities are shown. Check/uncheck to add/remove quantities. Use "Select All" to see all available options:',-1),Se={key:0,class:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg"},Fe={class:"text-sm text-red-600"},Pe={key:0,class:"sm:col-span-3"},Oe={class:"flex items-center space-x-3 mb-4 p-3 bg-blue-50 rounded-lg"},De={class:"grid grid-cols-1 gap-4"},Be={class:"text-sm text-gray-500 mt-1"},je={key:1,class:"sm:col-span-3"},Me={class:"flex items-center space-x-3 mb-4 p-3 bg-green-50 rounded-lg"},Ee={class:"grid grid-cols-1 gap-4"},Te={class:"text-sm text-gray-500 mt-1"},ze={key:2,class:"sm:col-span-3"},Le={class:"flex items-center space-x-3 mb-4 p-3 bg-yellow-50 rounded-lg"},Ie={class:"grid grid-cols-1 gap-4"},Ke={class:"text-sm text-gray-500 mt-1"},Ge={key:3,class:"sm:col-span-3"},He={class:"flex items-center space-x-3 mb-4 p-3 bg-purple-50 rounded-lg"},Ze={class:"grid grid-cols-1 gap-4"},Je={class:"text-sm text-gray-500 mt-1"},Re={key:4,class:"sm:col-span-12"},We={class:"bg-green-50 border border-green-200 p-6 rounded-lg"},Xe={class:"flex items-center justify-between"},Ye={class:"text-lg font-semibold text-green-800"},et=e("p",{class:"text-sm text-green-600 mt-1"}," Based on selected quantities and pricing ",-1),tt=e("div",{class:"text-right"},[e("svg",{class:"w-8 h-8 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z","clip-rule":"evenodd"})])],-1),at={class:"sm:col-span-12 mb-6"},st=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Lead Information",-1),lt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg"},dt=e("p",{class:"text-sm font-semibold text-gray-900"},"Client Name",-1),ot={class:"text-sm text-gray-700"},nt=e("p",{class:"text-sm font-semibold text-gray-900"},"County",-1),it={class:"text-sm text-gray-700"},rt=e("p",{class:"text-sm font-semibold text-gray-900"},"Dimensions",-1),ct={class:"text-sm text-gray-700"},ut=e("p",{class:"text-sm font-semibold text-gray-900"},"Open Size",-1),_t={class:"text-sm text-gray-700"},mt=e("p",{class:"text-sm font-semibold text-gray-900"},"Box Style",-1),yt={class:"text-sm text-gray-700"},gt=e("p",{class:"text-sm font-semibold text-gray-900"},"Stock",-1),vt={class:"text-sm text-gray-700"},xt=e("p",{class:"text-sm font-semibold text-gray-900"},"Lamination",-1),pt={class:"text-sm text-gray-700"},qt=e("p",{class:"text-sm font-semibold text-gray-900"},"Printing",-1),ft={class:"text-sm text-gray-700"},bt={key:0},ht=e("p",{class:"text-sm font-semibold text-gray-900"},"Add-ons",-1),kt={class:"text-sm text-gray-700"},wt={class:"sm:col-span-3"},At=e("p",{class:"text-sm text-gray-500 mt-1"},"Add tracking number when order is shipped",-1),Vt={class:"sm:col-span-3"},Ct={class:"relative mt-2"},Qt={class:"sm:col-span-3"},Ut=["value"],Nt={class:"sm:col-span-3"},$t=["value"],St={class:"sm:col-span-12"},Ft={class:"flex mt-6 items-center justify-between"},Pt={class:"ml-auto flex items-center justify-end gap-x-6"},Ot=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),Kt={__name:"Edit",props:{data:{type:Object,required:!0},productionStages:{type:Array,required:!0}},setup(s){const i=s;k(!0);const p=k(!!i.data.selected_qty_1),q=k(!!i.data.selected_qty_2),f=k(!!i.data.selected_qty_3),b=k(!!i.data.selected_qty_4),Q=k(0),t=Z({selected_qty_1:i.data.lead.qty_1||"",selected_qty_2:i.data.lead.qty_2||"",selected_qty_3:i.data.lead.qty_3||"",selected_qty_4:i.data.lead.qty_4||"",tracking_number:i.data.tracking_number||"",expected_delivery:i.data.expected_delivery||"",actual_delivery:i.data.actual_delivery||"",notes:i.data.notes||"",production_stage:i.data.production_stage}),J=()=>p.value||q.value||f.value||b.value?p.value&&!t.selected_qty_1?(alert("Please enter a value for Quantity 1."),!1):q.value&&!t.selected_qty_2?(alert("Please enter a value for Quantity 2."),!1):f.value&&!t.selected_qty_3?(alert("Please enter a value for Quantity 3."),!1):b.value&&!t.selected_qty_4?(alert("Please enter a value for Quantity 4."),!1):!0:(alert("Please select at least one quantity for the order."),!1),R=()=>{if(!J())return;const n=W();Z(n).put(route("orders.update",i.data.id),{preserveScroll:!0})},h=n=>{var A,V;const a={"United Kingdom":{locale:"en-GB",currency:"GBP"},"United States":{locale:"en-US",currency:"USD"},Canada:{locale:"en-CA",currency:"CAD"},Australia:{locale:"en-AU",currency:"AUD"}},u=((V=(A=i.data.lead)==null?void 0:A.county)==null?void 0:V.name)||"UK",_=Object.keys(a).find($=>u.toLowerCase().includes($.toLowerCase())),{locale:m,currency:w}=a[_]||a.UK,N=new Intl.NumberFormat(m,{style:"currency",currency:w,currencyDisplay:"symbol"}).format(n);return`${w} ${N}`},F=()=>{var a,u,_,m;let n=0;p.value&&t.selected_qty_1&&((a=i.data.quotation)!=null&&a.price_qty_1)&&(n+=parseFloat(t.selected_qty_1)*parseFloat(i.data.quotation.price_qty_1)),q.value&&t.selected_qty_2&&((u=i.data.quotation)!=null&&u.price_qty_2)&&(n+=parseFloat(t.selected_qty_2)*parseFloat(i.data.quotation.price_qty_2)),f.value&&t.selected_qty_3&&((_=i.data.quotation)!=null&&_.price_qty_3)&&(n+=parseFloat(t.selected_qty_3)*parseFloat(i.data.quotation.price_qty_3)),b.value&&t.selected_qty_4&&((m=i.data.quotation)!=null&&m.price_qty_4)&&(n+=parseFloat(t.selected_qty_4)*parseFloat(i.data.quotation.price_qty_4)),Q.value=n},U=(n,a)=>{F()},W=()=>{var a,u,_,m;const n={status:t.status,tracking_number:t.tracking_number,expected_delivery:t.expected_delivery,actual_delivery:t.actual_delivery,production_stage:t.production_stage,notes:t.notes,selected_qty_1:null,selected_qty_2:null,selected_qty_3:null,selected_qty_4:null,price_qty_1:null,price_qty_2:null,price_qty_3:null,price_qty_4:null};return p.value&&t.selected_qty_1&&(n.selected_qty_1=t.selected_qty_1,n.price_qty_1=(a=i.data.quotation)==null?void 0:a.price_qty_1),q.value&&t.selected_qty_2&&(n.selected_qty_2=t.selected_qty_2,n.price_qty_2=(u=i.data.quotation)==null?void 0:u.price_qty_2),f.value&&t.selected_qty_3&&(n.selected_qty_3=t.selected_qty_3,n.price_qty_3=(_=i.data.quotation)==null?void 0:_.price_qty_3),b.value&&t.selected_qty_4&&(n.selected_qty_4=t.selected_qty_4,n.price_qty_4=(m=i.data.quotation)==null?void 0:m.price_qty_4),n.total_amount=Q.value,n};Y([()=>t.selected_qty_1,()=>p.value,()=>t.selected_qty_2,()=>q.value,()=>t.selected_qty_3,()=>f.value,()=>t.selected_qty_4,()=>b.value],F),F();const X=(n,a)=>{t.production_stage=n};return(n,a)=>(y(),g(ee,null,[l(o(te),{title:"Orders"}),l(le,null,{default:P(()=>{var u,_,m,w,N,A,V,$,O,D,B,j,M,E,T,z,L,I,K,G,H;return[e("div",re,[e("div",ce,[e("div",null,[e("h2",ue," Edit Order - "+r(s.data.order_number),1),e("p",_e," Client: "+r(s.data.lead.client_name),1)]),e("div",me,[s.data.is_confirmed?(y(),g("span",ye," ✓ Confirmed ")):x("",!0)])]),e("div",ge,[ve,e("div",xe,[e("div",null,[pe,e("p",qe,r(((u=s.data.quotation)==null?void 0:u.quotation_number)||"N/A"),1)]),e("div",null,[fe,e("p",be,r(s.data.lead.lead_number||"N/A"),1)]),e("div",null,[he,e("p",ke,r(h(s.data.total_amount)),1)]),e("div",null,[we,e("p",Ae,r((_=s.data.creator)==null?void 0:_.first_name)+" "+r((m=s.data.creator)==null?void 0:m.last_name),1)])])]),e("form",{onSubmit:ae(R,["prevent"])},[e("div",Ce,[e("div",Qe,[e("div",Ue,[Ne,$e,n.$page.props.errors.quantities?(y(),g("div",Se,[e("p",Fe,r(n.$page.props.errors.quantities),1)])):x("",!0)]),(w=s.data.lead)!=null&&w.qty_1&&((N=s.data.quotation)!=null&&N.price_qty_1)?(y(),g("div",Pe,[e("div",Oe,[l(S,{checked:p.value,"onUpdate:checked":[a[0]||(a[0]=d=>p.value=d),a[1]||(a[1]=d=>U(1,d))]},null,8,["checked"]),l(c,{value:"Client wants Quantity 1",class:"text-base font-medium text-blue-800"})]),e("div",De,[e("div",null,[l(c,{for:"selected_qty_1",value:`Order Qty 1 (Available: ${s.data.lead.qty_1})`},null,8,["value"]),l(C,{id:"selected_qty_1",type:"number",modelValue:o(t).selected_qty_1,"onUpdate:modelValue":a[2]||(a[2]=d=>o(t).selected_qty_1=d),max:s.data.lead.qty_1,min:"1",placeholder:`Max: ${s.data.lead.qty_1}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(v,{message:o(t).errors.selected_qty_1},null,8,["message"]),e("p",Be,"Price: "+r(h(s.data.quotation.price_qty_1))+" per unit",1)])])])):x("",!0),(A=s.data.lead)!=null&&A.qty_2&&((V=s.data.quotation)!=null&&V.price_qty_2)?(y(),g("div",je,[e("div",Me,[l(S,{checked:q.value,"onUpdate:checked":[a[3]||(a[3]=d=>q.value=d),a[4]||(a[4]=d=>U(2,d))]},null,8,["checked"]),l(c,{value:"Client wants Quantity 2",class:"text-base font-medium text-green-800"})]),e("div",Ee,[e("div",null,[l(c,{for:"selected_qty_2",value:`Order Qty 2 (Available: ${s.data.lead.qty_2})`},null,8,["value"]),l(C,{id:"selected_qty_2",type:"number",modelValue:o(t).selected_qty_2,"onUpdate:modelValue":a[5]||(a[5]=d=>o(t).selected_qty_2=d),max:s.data.lead.qty_2,min:"1",placeholder:`Max: ${s.data.lead.qty_2}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(v,{message:o(t).errors.selected_qty_2},null,8,["message"]),e("p",Te,"Price: "+r(h(s.data.quotation.price_qty_2))+" per unit",1)])])])):x("",!0),($=s.data.lead)!=null&&$.qty_3&&((O=s.data.quotation)!=null&&O.price_qty_3)?(y(),g("div",ze,[e("div",Le,[l(S,{checked:f.value,"onUpdate:checked":[a[6]||(a[6]=d=>f.value=d),a[7]||(a[7]=d=>U(3,d))]},null,8,["checked"]),l(c,{value:"Client wants Quantity 3",class:"text-base font-medium text-yellow-800"})]),e("div",Ie,[e("div",null,[l(c,{for:"selected_qty_3",value:`Order Qty 3 (Available: ${s.data.lead.qty_3})`},null,8,["value"]),l(C,{id:"selected_qty_3",type:"number",modelValue:o(t).selected_qty_3,"onUpdate:modelValue":a[8]||(a[8]=d=>o(t).selected_qty_3=d),max:s.data.lead.qty_3,min:"1",placeholder:`Max: ${s.data.lead.qty_3}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(v,{message:o(t).errors.selected_qty_3},null,8,["message"]),e("p",Ke,"Price: "+r(h(s.data.quotation.price_qty_3))+" per unit",1)])])])):x("",!0),(D=s.data.lead)!=null&&D.qty_4&&((B=s.data.quotation)!=null&&B.price_qty_4)?(y(),g("div",Ge,[e("div",He,[l(S,{checked:b.value,"onUpdate:checked":[a[9]||(a[9]=d=>b.value=d),a[10]||(a[10]=d=>U(4,d))]},null,8,["checked"]),l(c,{value:"Client wants Quantity 4",class:"text-base font-medium text-purple-800"})]),e("div",Ze,[e("div",null,[l(c,{for:"selected_qty_4",value:`Order Qty 4 (Available: ${s.data.lead.qty_4})`},null,8,["value"]),l(C,{id:"selected_qty_4",type:"number",modelValue:o(t).selected_qty_4,"onUpdate:modelValue":a[11]||(a[11]=d=>o(t).selected_qty_4=d),max:s.data.lead.qty_4,min:"1",placeholder:`Max: ${s.data.lead.qty_4}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(v,{message:o(t).errors.selected_qty_4},null,8,["message"]),e("p",Je,"Price: "+r(h(s.data.quotation.price_qty_4))+" per unit",1)])])])):x("",!0),Q.value>0?(y(),g("div",Re,[e("div",We,[e("div",Xe,[e("div",null,[e("p",Ye," Updated Order Total: "+r(h(Q.value.toFixed(2))),1),et]),tt])])])):x("",!0),e("div",at,[st,e("div",lt,[e("div",null,[dt,e("p",ot,r(((j=s.data.lead)==null?void 0:j.client_name)||"N/A"),1)]),e("div",null,[nt,e("p",it,r(((E=(M=s.data.lead)==null?void 0:M.county)==null?void 0:E.name)||"N/A"),1)]),e("div",null,[rt,e("p",ct,r(((T=s.data.lead)==null?void 0:T.dimensions)||"N/A"),1)]),e("div",null,[ut,e("p",_t,r(((z=s.data.lead)==null?void 0:z.open_size)||"N/A"),1)]),e("div",null,[mt,e("p",yt,r(((L=s.data.lead)==null?void 0:L.box_style)||"N/A"),1)]),e("div",null,[gt,e("p",vt,r(((I=s.data.lead)==null?void 0:I.stock)||"N/A"),1)]),e("div",null,[xt,e("p",pt,r(((K=s.data.lead)==null?void 0:K.lamination)||"N/A"),1)]),e("div",null,[qt,e("p",ft,r(((G=s.data.lead)==null?void 0:G.printing)||"N/A"),1)]),(H=s.data.lead)!=null&&H.add_ons?(y(),g("div",bt,[ht,e("p",kt,r(s.data.lead.add_ons),1)])):x("",!0)])]),e("div",wt,[l(c,{for:"tracking_number",value:"Tracking Number"}),l(C,{id:"tracking_number",type:"text",modelValue:o(t).tracking_number,"onUpdate:modelValue":a[12]||(a[12]=d=>o(t).tracking_number=d),placeholder:"Enter tracking number"},null,8,["modelValue"]),l(v,{message:o(t).errors.tracking_number},null,8,["message"]),At]),e("div",Vt,[l(c,{for:"production_stage",value:"Production Stage"}),e("div",Ct,[l(ie,{options:s.productionStages,modelValue:o(t).production_stage,"onUpdate:modelValue":a[13]||(a[13]=d=>o(t).production_stage=d),onOnchange:X},null,8,["options","modelValue"])]),l(v,{message:o(t).errors.production_stage},null,8,["message"])]),e("div",Qt,[l(c,{for:"expected_delivery",value:"Expected Delivery Date"}),e("input",{type:"date",class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",value:o(t).expected_delivery?o(t).expected_delivery.slice(0,10):"",onInput:a[14]||(a[14]=d=>o(t).expected_delivery=d.target.value)},null,40,Ut),l(v,{message:o(t).errors.expected_delivery},null,8,["message"])]),e("div",Nt,[l(c,{for:"actual_delivery",value:"Actual Delivery Date"}),e("input",{type:"date",class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",value:o(t).actual_delivery?o(t).actual_delivery.slice(0,10):"",onInput:a[15]||(a[15]=d=>o(t).actual_delivery=d.target.value)},null,40,$t),l(v,{message:o(t).errors.actual_delivery},null,8,["message"])]),e("div",St,[l(c,{for:"notes",value:"Order Notes"}),l(ne,{id:"notes",modelValue:o(t).notes,"onUpdate:modelValue":a[16]||(a[16]=d=>o(t).notes=d),rows:"4",placeholder:"Add any notes about this order..."},null,8,["modelValue"]),l(v,{message:o(t).errors.notes},null,8,["message"])])])]),e("div",Ft,[e("div",Pt,[l(de,{href:n.route("orders.index")},{svg:P(()=>[Ot]),_:1},8,["href"]),l(oe,{disabled:o(t).processing},{default:P(()=>[se(" Save ")]),_:1},8,["disabled"])])])],40,Ve)])]}),_:1})],64))}};export{Kt as default};
