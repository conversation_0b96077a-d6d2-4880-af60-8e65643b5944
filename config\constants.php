<?php

$defaultPath = public_path() . '/uploads/';
$viewPath = '/uploads/';

//$monthNameList = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

return [

    'perPage' => 20,

    'uploadFilePath' => [
        'companyDocument'       => ['default' => $defaultPath. 'companyprofile/',         'view' => $viewPath. 'companyprofile/'],
        'leadDocument'          => ['default' => $defaultPath. 'leads/',          'view' => $viewPath. 'leads/'],
        'quotationDocument'     => ['default' => $defaultPath. 'quotations/',     'view' => $viewPath. 'quotations/'],
        'orderDocument'         => ['default' => $defaultPath. 'orders/',         'view' => $viewPath. 'orders/'],
    ],

    //'months' => array_map(fn($month) => ['id' => $month, 'name' => $month], $monthNameList),
    'months' => [
        [ "id"=> "January",     "name"=> "January"],
        [ "id"=> "February",    "name"=> "February"],
        [ "id"=> "March",       "name"=> "March"],
        [ "id"=> "April",       "name"=> "April"],
        [ "id"=> "May",         "name"=> "May"],
        [ "id"=> "June",        "name"=> "June"],
        [ "id"=> "July",        "name"=> "July"],
        [ "id"=> "August",      "name"=> "August"],
        [ "id"=> "September",   "name"=> "September"],
        [ "id"=> "October",     "name"=> "October"],
        [ "id"=> "November",    "name"=> "November"],
        [ "id"=> "December",    "name"=> "December"]
    ],

    'pageTypes' => [
        ['id' => 'portrait', 'name' => 'Portrait'],
        ['id' => 'landscape', 'name' => 'Landscape']
    ],

    'taskStatus' => [
        ['id' => '', 'name' => 'All Status'],
        ['id' => 'pending', 'name' => 'Pending'],
        ['id' => 'in_progress', 'name' => 'In Progress'],
        ['id' => 'completed', 'name' => 'Completed'],
        ['id' => 'cancelled', 'name' => 'Cancelled']
    ],

    'taskTypes' => [
        ['id' => '', 'name' => 'All Types'],
        ['id' => 'call', 'name' => 'Phone Call'],
        ['id' => 'follow_up', 'name' => 'Follow Up'],
        ['id' => 'meeting', 'name' => 'Meeting'],
        ['id' => 'email', 'name' => 'Email'],
        ['id' => 'quote_follow_up', 'name' => 'Quote Follow Up'],
        ['id' => 'order_follow_up', 'name' => 'Order Follow Up'],
        ['id' => 'general', 'name' => 'General Task'],
        ['id' => 'reminder', 'name' => 'Reminder']
    ],

    'taskPriorities' => [
        ['id' => '', 'name' => 'All Priorities'],
        ['id' => 'low', 'name' => 'Low'],
        ['id' => 'medium', 'name' => 'Medium'],
        ['id' => 'high', 'name' => 'High'],
        ['id' => 'urgent', 'name' => 'Urgent']
    ],

    'types' => [
        ['id' => '', 'name' => 'Select Type'],
        ['id' => 'call', 'name' => '📞 Phone Call'],
        ['id' => 'follow_up', 'name' => '🔄 Follow Up'],
        ['id' => 'meeting', 'name' => '🤝 Meeting'],
        ['id' => 'email', 'name' => '📧 Email'],
        ['id' => 'quote_follow_up', 'name' => '💰 Quote Follow Up'],
        ['id' => 'order_follow_up', 'name' => '📦 Order Follow Up'],
        ['id' => 'general', 'name' => '📋 General Task'],
        ['id' => 'reminder', 'name' => '⏰ Reminder']
    ],

    'priorities' => [
        ['id' => '', 'name' => 'Select Priority'],
        ['id' => 'low', 'name' => '🟢 Low'],
        ['id' => 'medium', 'name' => '🟡 Medium'],
        ['id' => 'high', 'name' => '🟠 High'],
        ['id' => 'urgent', 'name' => '🔴 Urgent']
    ],

    'status' => [
        ['id' => '', 'name' => 'Select Status'],
        ['id' => 'pending', 'name' => '⏳ Pending'],
        ['id' => 'in_progress', 'name' => '🔄 In Progress'],
        ['id' => 'completed', 'name' => '✅ Completed'],
        ['id' => 'cancelled', 'name' => '❌ Cancelled']
    ],

    'productionStages' => [
        ['id' => 'Pre-press', 'name' => 'Pre-press'],
        ['id' => 'Printing', 'name' => 'Printing'],
        ['id' => 'Lamination', 'name' => 'Lamination'],
        ['id' => 'Die cutting', 'name' => 'Die cutting'],
        ['id' => 'Quality assurance', 'name' => 'Quality assurance']
    ],

    'statusOptions' => [
        ['id' => 'confirmed', 'name' => 'Confirmed'],
        ['id' => 'under_production', 'name' => 'Under Production'],
        ['id' => 'shipped', 'name' => 'Shipped'],
        ['id' => 'delivered', 'name' => 'Delivered']
    ]






];
