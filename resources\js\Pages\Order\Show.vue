<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import { Head } from '@inertiajs/vue3';

const props = defineProps({
    order: {
        type: Object,
        required: true
    }
});

const getStatusClass = (status) => {
    const classes = {
        'pending': 'bg-yellow-100 text-yellow-800',
        'confirmed': 'bg-blue-100 text-blue-800',
        'under_production': 'bg-purple-100 text-purple-800',
        'shipped': 'bg-indigo-100 text-indigo-800',
        'delivered': 'bg-green-100 text-green-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
};

const formatDate = (date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
};

const formatCurrency = (amount) => {
    const countyCurrencyMap = {
        'United Kingdom': { locale: 'en-GB', currency: 'GBP' },
        'United States': { locale: 'en-US', currency: 'USD' },
        'Canada': { locale: 'en-CA', currency: 'CAD' },
        'Australia': { locale: 'en-AU', currency: 'AUD' }
    };

    const countyName = props.order.lead?.county?.name || 'UK'; // default fallback
    const matchedKey = Object.keys(countyCurrencyMap).find(key =>
        countyName.toLowerCase().includes(key.toLowerCase())
    );

    const { locale, currency } = countyCurrencyMap[matchedKey] || countyCurrencyMap['UK'];

    const formattedAmount = new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency,
        // currencyDisplay: 'code',  // Shows USD, AUD, CAD instead of just $
        currencyDisplay: 'symbol'  // Keep normal symbol like $ or £
    }).format(amount);
    return `${currency} ${formattedAmount}`;
};

const getStatusDisplayName = (status) => {
    const names = {
        'pending': 'Pending',
        'confirmed': 'Confirmed',
        'under_production': 'Under Production',
        'shipped': 'Shipped',
        'delivered': 'Delivered'
    };
    return names[status] || status;
};

const downloadPdf = () => {
    window.open(route('orders.pdf', props.order.id), '_blank');
};
</script>

<template>
    <Head title="Orders" />

    <AdminLayout>
        <div class="animate-top">
            <!-- Header -->
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ order.order_number }}</h1>
                    <p class="text-gray-600 mt-1">Order Details</p>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-8">
                    <!-- Lead Information -->
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Lead Information</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Client Name</label>
                                <p class="mt-1 text-sm text-gray-700">{{ order.lead?.client_name || 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">County</label>
                                <p class="mt-1 text-sm text-gray-700">{{ order.lead?.county?.name || 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Lead Number</label>
                                <p class="mt-1 text-sm text-gray-700">{{ order.lead?.lead_number || 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Lead Status</label>
                                <p class="mt-1 text-sm text-gray-700">{{ order.lead?.status || 'N/A' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Product Specifications (from Lead) -->
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Product Specifications</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Dimensions</label>
                                <p class="mt-1 text-sm text-gray-700">{{ order.lead?.dimensions || 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Open Size</label>
                                <p class="mt-1 text-sm text-gray-700">{{ order.lead?.open_size || 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Box Style</label>
                                <p class="mt-1 text-sm text-gray-700">{{ order.lead?.box_style || 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Stock</label>
                                <p class="mt-1 text-sm text-gray-700">{{ order.lead?.stock || 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Lamination</label>
                                <p class="mt-1 text-sm text-gray-700">{{ order.lead?.lamination || 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Printing</label>
                                <p class="mt-1 text-sm text-gray-700">{{ order.lead?.printing || 'N/A' }}</p>
                            </div>
                            <div v-if="order.lead?.add_ons" class="md:col-span-2">
                                <label class="block text-sm font-semibold text-gray-900">Add-ons</label>
                                <p class="mt-1 text-sm text-gray-700">{{ order.lead.add_ons }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Order Information -->
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Order Information</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Status</label>
                                <span :class="['inline-flex px-3 py-1 rounded-full text-sm font-semibold mt-1', getStatusClass(order.status)]">
                                    {{ getStatusDisplayName(order.status) }}
                                </span>
                            </div>
                            <div v-if="order.quotation">
                                <label class="block text-sm font-semibold text-gray-900">Quotation Number</label>
                                <p class="mt-1 text-sm text-gray-700">{{ order.quotation.quotation_number }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Created By</label>
                                <p class="mt-1 text-sm text-gray-700">{{ order.creator?.first_name }} {{ order.creator?.last_name }}</p>
                            </div>
                            <div v-if="order.tracking_number">
                                <label class="block text-sm font-semibold text-gray-900">Tracking Number</label>
                                <p class="mt-1 text-sm text-gray-700">{{ order.tracking_number }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Expected Delivery</label>
                                <p class="mt-1 text-sm text-gray-700">{{ formatDate(order.expected_delivery) }}</p>
                            </div>
                            <div v-if="order.actual_delivery">
                                <label class="block text-sm font-semibold text-gray-900">Actual Delivery</label>
                                <p class="mt-1 text-sm text-gray-700">{{ formatDate(order.actual_delivery) }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Order Quantities -->
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Order Quantities</h2>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider">Quantity</th>
                                        <th class="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider">Unit Price</th>
                                        <th class="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider">Total</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-if="order.selected_qty_1 && order.price_qty_1">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ parseInt(order.selected_qty_1).toLocaleString() }} pcs</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ formatCurrency(order.price_qty_1) }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700">{{ formatCurrency(order.selected_qty_1 * order.price_qty_1) }}</td>
                                    </tr>
                                    <tr v-if="order.selected_qty_2 && order.price_qty_2">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ parseInt(order.selected_qty_2).toLocaleString() }} pcs</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ formatCurrency(order.price_qty_2) }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700">{{ formatCurrency(order.selected_qty_2 * order.price_qty_2) }}</td>
                                    </tr>
                                    <tr v-if="order.selected_qty_3 && order.price_qty_3">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ parseInt(order.selected_qty_3).toLocaleString() }} pcs</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ formatCurrency(order.price_qty_3) }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700">{{ formatCurrency(order.selected_qty_3 * order.price_qty_3) }}</td>
                                    </tr>
                                    <tr v-if="order.selected_qty_4 && order.price_qty_4">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ parseInt(order.selected_qty_4).toLocaleString() }} pcs</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ formatCurrency(order.price_qty_4) }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700">{{ formatCurrency(order.selected_qty_4 * order.price_qty_4) }}</td>
                                    </tr>
                                </tbody>
                                <tfoot class="bg-gray-50">
                                    <tr>
                                        <td colspan="2" class="px-6 py-4 text-sm font-semibold text-gray-900 text-right">Total Amount:</td>
                                        <td class="px-6 py-4 text-sm font-bold text-green-600">{{ formatCurrency(order.total_amount) }}</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div v-if="order.notes" class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Notes</h2>
                        <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ order.notes }}</p>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Quick Actions -->
                    <div class="bg-white shadow rounded-lg p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-6">Quick Actions</h3>
                        <div class="space-y-3 w-full">
                            <ActionLink :href="route('orders.edit', order.id)" class="w-full">
                                <template #svg>
                                    <button class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-2">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125" />
                                        </svg>
                                        Edit Order
                                    </button>
                                </template>
                            </ActionLink>
                            <ActionLink :href="route('orders.index')" class="w-full">
                                <template #svg>
                                    <button class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-2">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3" />
                                        </svg>
                                        Back to Orders
                                    </button>
                                </template>
                            </ActionLink>
                        </div>
                    </div>

                    <!-- Timeline -->
                    <div class="bg-white shadow rounded-lg p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-6">Timeline</h3>
                        <div class="space-y-4">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                                <div>
                                    <p class="text-sm font-semibold text-gray-900">Order Created</p>
                                    <p class="text-xs text-gray-500">{{ formatDate(order.created_at) }}</p>
                                </div>
                            </div>
                            <div v-if="order.is_confirmed" class="flex items-start space-x-3">
                                <div class="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                                <div>
                                    <p class="text-sm font-semibold text-gray-900">Order Confirmed</p>
                                    <p class="text-xs text-gray-500">{{ formatDate(order.confirmed_at) }}</p>
                                </div>
                            </div>
                            <div v-if="order.updated_at !== order.created_at" class="flex items-start space-x-3">
                                <div class="flex-shrink-0 w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                                <div>
                                    <p class="text-sm font-semibold text-gray-900">Last Updated</p>
                                    <p class="text-xs text-gray-500">{{ formatDate(order.updated_at) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
