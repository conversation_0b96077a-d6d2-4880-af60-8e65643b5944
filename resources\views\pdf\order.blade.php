<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Order - {{ $order->order_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .company-info {
            float: left;
            width: 50%;
        }
        .order-info {
            float: right;
            width: 45%;
            text-align: right;
        }
        .clear {
            clear: both;
        }
        .section {
            margin-bottom: 25px;
        }
        .section-title {
            background-color: #f3f4f6;
            padding: 10px;
            font-weight: bold;
            border-left: 4px solid #2563eb;
            margin-bottom: 15px;
        }
        .info-grid {
            display: table;
            width: 100%;
        }
        .info-row {
            display: table-row;
        }
        .info-label {
            display: table-cell;
            font-weight: bold;
            padding: 8px 0;
            width: 30%;
            vertical-align: top;
        }
        .info-value {
            display: table-cell;
            padding: 8px 0;
            vertical-align: top;
        }
        .quantities-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .quantities-table th,
        .quantities-table td {
            border: 1px solid #d1d5db;
            padding: 12px;
            text-align: left;
        }
        .quantities-table th {
            background-color: #f9fafb;
            font-weight: bold;
        }
        .quantities-table .amount {
            text-align: right;
        }
        .total-row {
            background-color: #f0f9ff;
            font-weight: bold;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-pending { background-color: #fef3c7; color: #92400e; }
        .status-confirmed { background-color: #dbeafe; color: #1e40af; }
        .status-under_production { background-color: #e9d5ff; color: #7c3aed; }
        .status-shipped { background-color: #c7d2fe; color: #3730a3; }
        .status-delivered { background-color: #d1fae5; color: #065f46; }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #d1d5db;
            text-align: center;
            font-size: 12px;
            color: #6b7280;
        }
        .tracking-info {
            background-color: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .tracking-number {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 16px;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-info">
            <h1 style="margin: 0; color: #2563eb;">{{ $company['name'] }}</h1>
            <p style="margin: 5px 0;">{{ $company['address'] }}</p>
            <p style="margin: 5px 0;">Phone: {{ $company['phone'] }}</p>
            <p style="margin: 5px 0;">Email: {{ $company['email'] }}</p>
        </div>
        <div class="order-info">
            <h2 style="margin: 0; color: #2563eb;">ORDER</h2>
            <p style="margin: 5px 0; font-size: 18px; font-weight: bold;">{{ $order->order_number }}</p>
            <p style="margin: 5px 0;">Date: {{ $order->created_at->format('F j, Y') }}</p>
            <span class="status-badge status-{{ $order->status }}">{{ ucfirst(str_replace('_', ' ', $order->status)) }}</span>
        </div>
        <div class="clear"></div>
    </div>

    <!-- Client Information -->
    <div class="section">
        <div class="section-title">Client Information</div>
        <div class="info-grid">
            <div class="info-row">
                <div class="info-label">Client Name:</div>
                <div class="info-value">{{ $order->lead->client_name }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">County:</div>
                <div class="info-value">{{ $order->county->name ?? 'N/A' }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Quotation Reference:</div>
                <div class="info-value">{{ $order->quotation->quotation_number ?? 'N/A' }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Sales Agent:</div>
                <div class="info-value">{{ $order->creator->first_name ?? '' }} {{ $order->creator->last_name ?? '' }}</div>
            </div>
        </div>
    </div>

    <!-- Order Status & Tracking -->

    <div class="section">
        <div class="section-title">Order Status</div>
        <div class="info-grid">
            @if($order->expected_delivery)
            <div class="info-row">
                <div class="info-label">Expected Delivery:</div>
                <div class="info-value">{{ \Carbon\Carbon::parse($order->expected_delivery)->format('F j, Y') }}</div>
            </div>
            @endif
            @if($order->actual_delivery)
            <div class="info-row">
                <div class="info-label">Actual Delivery:</div>
                <div class="info-value">{{ \Carbon\Carbon::parse($order->actual_delivery)->format('F j, Y') }}</div>
            </div>
            @endif
        </div>

        @if($order->tracking_number)
        <div class="tracking-info">
            <strong>Tracking Information:</strong><br>
            <span class="tracking-number">{{ $order->tracking_number }}</span>
        </div>
        @endif
    </div>

    <!-- Product Specifications -->
    <div class="section">
        <div class="section-title">Product Specifications</div>
        <div class="info-grid">
            <div class="info-row">
                <div class="info-label">Dimensions:</div>
                <div class="info-value">{{ $order->lead->dimensions }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Open Size:</div>
                <div class="info-value">{{ $order->lead->open_size }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Box Style:</div>
                <div class="info-value">{{ $order->lead->box_style }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Stock:</div>
                <div class="info-value">{{ $order->lead->stock }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Lamination:</div>
                <div class="info-value">{{ $order->lead->lamination }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Printing:</div>
                <div class="info-value">{{ $order->lead->printing }}</div>
            </div>
            @if($order->lead->add_ons)
            <div class="info-row">
                <div class="info-label">Add-ons:</div>
                <div class="info-value">{{ $order->lead->add_ons }}</div>
            </div>
            @endif
        </div>
    </div>

    <!-- Order Quantities & Pricing -->
    <div class="section">
        <div class="section-title">Order Quantities & Pricing</div>
        <table class="quantities-table">
            <thead>
                <tr>
                    <th>Quantity</th>
                    <th>Unit Price</th>
                    <th class="amount">Line Total</th>
                </tr>
            </thead>
            <tbody>
                @if($order->selected_qty_1 && $order->price_qty_1)
                <tr>
                    <td>{{ number_format($order->selected_qty_1) }} pcs</td>
                    <td>{{ $currencySymbol }}{{ number_format($order->price_qty_1, 2) }}</td>
                    <td class="amount">{{ $currencySymbol }}{{ number_format($order->selected_qty_1 * $order->price_qty_1, 2) }}</td>
                </tr>
                @endif
                @if($order->selected_qty_2 && $order->price_qty_2)
                <tr>
                    <td>{{ number_format($order->selected_qty_2) }} pcs</td>
                    <td>{{ $currencySymbol }}{{ number_format($order->price_qty_2, 2) }}</td>
                    <td class="amount">{{ $currencySymbol }}{{ number_format($order->selected_qty_2 * $order->price_qty_2, 2) }}</td>
                </tr>
                @endif
                @if($order->selected_qty_3 && $order->price_qty_3)
                <tr>
                    <td>{{ number_format($order->selected_qty_3) }} pcs</td>
                    <td>{{ $currencySymbol }}{{ number_format($order->price_qty_3, 2) }}</td>
                    <td class="amount">{{ $currencySymbol }}{{ number_format($order->selected_qty_3 * $order->price_qty_3, 2) }}</td>
                </tr>
                @endif
                @if($order->selected_qty_4 && $order->price_qty_4)
                <tr>
                    <td>{{ number_format($order->selected_qty_4) }} pcs</td>
                    <td>{{ $currencySymbol }}{{ number_format($order->price_qty_4, 2) }}</td>
                    <td class="amount">{{ $currencySymbol }}{{ number_format($order->selected_qty_4 * $order->price_qty_4, 2) }}</td>
                </tr>
                @endif
            </tbody>
            <tfoot>
                <tr class="total-row">
                    <td colspan="2"><strong>Total Amount</strong></td>
                    <td class="amount"><strong>{{ $currencySymbol }}{{ number_format($order->total_amount, 2) }}</strong></td>
                </tr>
            </tfoot>
        </table>
    </div>

    <!-- Notes -->
    @if($order->notes)
    <div class="section">
        <div class="section-title">Order Notes</div>
        <p style="line-height: 1.6;">{{ $order->notes }}</p>
    </div>
    @endif

    <!-- Footer -->
    <div class="footer">
        <p>This is a computer-generated document. No signature is required.</p>
        <p>Generated on {{ now()->format('F j, Y \a\t g:i A') }}</p>
    </div>
</body>
</html>
