<script setup>
import { ref, defineProps, defineEmits } from 'vue';

const props = defineProps(['switchValue','userId']);
const emits = defineEmits(['updateSwitchValue']);

const toggleSwitch = () => {
  emits('updateSwitchValue', !props.switchValue, props.userId);
};
</script>

<template>
    <button
    type="button"
    :class="[
      'relative inline-flex items-center flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2',
      props.switchValue ? 'bg-indigo-600' : 'bg-gray-300',
      'w-12 h-7 sm:w-14 sm:h-8'
    ]"
    role="switch"
    :aria-checked="props.switchValue.toString()"
    @click="toggleSwitch"
  >
    <span class="sr-only">Toggle setting</span>
    <span
      :class="[
        'inline-block transform rounded-full bg-white shadow transition duration-200 ease-in-out',
        props.switchValue ? 'translate-x-5 sm:translate-x-6' : 'translate-x-1',
        'w-5 h-5 sm:w-6 sm:h-6'
      ]"
      aria-hidden="true"
    ></span>
  </button>
</template>


