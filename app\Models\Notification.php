<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'title', 'message', 'type', 'priority', 'user_id',
        'notifiable_type', 'notifiable_id', 'is_read', 'read_at',
        'scheduled_for', 'action_data'
    ];

    protected $casts = [
        'is_read' => 'boolean',
        'read_at' => 'datetime',
        'scheduled_for' => 'datetime',
        'action_data' => 'array',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function notifiable(): MorphTo
    {
        return $this->morphTo();
    }

    // Scopes
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeScheduled($query)
    {
        return $query->whereNotNull('scheduled_for')
                    ->where('scheduled_for', '<=', now());
    }

    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', ['high', 'urgent']);
    }

    // Accessors
    public function getPriorityColorAttribute(): string
    {
        return match($this->priority) {
            'low' => 'green',
            'medium' => 'yellow',
            'high' => 'orange',
            'urgent' => 'red',
            default => 'gray'
        };
    }

    public function getTypeIconAttribute(): string
    {
        return match($this->type) {
            'task_reminder' => 'clock',
            'task_overdue' => 'exclamation-triangle',
            'lead_update' => 'user-plus',
            'quotation_update' => 'file-text',
            'order_update' => 'shopping-cart',
            'system' => 'cog',
            'follow_up_due' => 'phone',
            'call_scheduled' => 'calendar',
            default => 'bell'
        };
    }

    // Methods
    public function markAsRead(): void
    {
        $this->update([
            'is_read' => true,
            'read_at' => now()
        ]);
    }

    public static function createForUser(int $userId, array $data): self
    {
        return static::create([
            'user_id' => $userId,
            'title' => $data['title'],
            'message' => $data['message'],
            'type' => $data['type'],
            'priority' => $data['priority'] ?? 'medium',
            'notifiable_type' => $data['notifiable_type'] ?? null,
            'notifiable_id' => $data['notifiable_id'] ?? null,
            'scheduled_for' => $data['scheduled_for'] ?? null,
            'action_data' => $data['action_data'] ?? null,
        ]);
    }

    public static function createTaskReminder(Task $task): self
    {
        return static::createForUser($task->assigned_to, [
            'title' => 'Task Reminder',
            'message' => "Task '{$task->title}' is due soon",
            'type' => 'task_reminder',
            'priority' => $task->priority,
            'notifiable_type' => Task::class,
            'notifiable_id' => $task->id,
            'action_data' => [
                'url' => route('tasks.show', $task->id),
                'action' => 'View Task'
            ]
        ]);
    }

    public static function createTaskOverdue(Task $task): self
    {
        return static::createForUser($task->assigned_to, [
            'title' => 'Task Overdue',
            'message' => "Task '{$task->title}' is overdue",
            'type' => 'task_overdue',
            'priority' => 'urgent',
            'notifiable_type' => Task::class,
            'notifiable_id' => $task->id,
            'action_data' => [
                'url' => route('tasks.show', $task->id),
                'action' => 'View Task'
            ]
        ]);
    }
}
