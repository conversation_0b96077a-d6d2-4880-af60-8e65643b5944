import{A as i,b as r,d as _,h as e,e as s,f as l,u as a,y as p,Z as d,p as h,m as u}from"./app-1bd09312.js";import{_ as m}from"./_plugin-vue_export-helper-c27b6911.js";const o=t=>(h("data-v-c7539881"),t=t(),u(),t),f={class:"min-h-screen loginview flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-slate-100"},g={class:"container shadow-md"},v=o(()=>e("h2",null,"Access Denied",-1)),x=o(()=>e("p",{class:"text-sm text-gray-500"},"You do not have permission to access this page/event. ",-1)),y=o(()=>e("p",{class:"text-sm text-gray-500"},"Please contact your administrator if you believe this is an error.",-1)),w=["href"],R={__name:"Registerv2",setup(t){return(c,A)=>{const n=i("ApplicationLogo");return r(),_("div",f,[e("div",null,[s(a(p),{href:"/"},{default:l(()=>[s(n,{class:"w-60 fill-current text-gray-500"})]),_:1})]),s(a(d),{title:"Register"}),e("div",g,[v,x,y,e("h4",null,[e("a",{href:c.route("login")},"Return to Login",8,w)])])])}}},B=m(R,[["__scopeId","data-v-c7539881"]]);export{B as default};
