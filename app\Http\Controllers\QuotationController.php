<?php

namespace App\Http\Controllers;

use App\Models\Quotation;
use App\Models\County;
use App\Models\Lead;
use App\Models\Document;
use App\Models\User;
use App\Models\Task;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use App\Services\NotificationService;
use App\Http\Requests\StoreQuotationRequest;
use Inertia\Inertia;
use App\Traits\QueryTrait;
use App\Traits\FileUploadTrait;
use Config;
use PDF;

class QuotationController extends Controller
{
    use QueryTrait, FileUploadTrait;

    public function index(Request $request)
    {
        $query = Quotation::with(['county', 'creator', 'lead.creator']);

        $county_id = $request->input('county_id');
        $agent_id = $request->input('agent_id');
        $status = $request->input('status');
        $isAdmin = auth()->user()->hasRole('Admin');

        // If not admin, show only quotations created by the logged-in user
        if (!auth()->user()->hasRole('Admin')) {
            $query->where('created_by', auth()->id());
        }

        if ($agent_id) {
            $query->whereHas('lead', function ($q) use ($agent_id) {
                $q->where('created_by', $agent_id);
            });
        }

        if ($county_id) {
            $query->whereHas('county', function ($q) use ($county_id) {
                $q->where('county_id', $county_id);
            });
        }

        if($status) {
            $query->where('status', $status);
        }

        $searchableFields = ['quotation_number', 'lead.client_name', 'lead.dimensions',
        'lead.open_size', 'lead.box_style', 'lead.stock', 'lead.lamination',  'lead.printing', 'lead.county.name',
         'status', 'lead.lead_number'];
        $this->searchAndSort($query, $request, $searchableFields);

        $data = $query->orderBy('id', 'desc')->paginate(20);

        $permissions = [
            'canConvertQuotation' => auth()->user()->can('Convert Quotations'),
            'canEditQuotation' => auth()->user()->can('Edit Quotations'),
            'canDeleteQuotation' => auth()->user()->can('Delete Quotations'),
        ];

        $counties = County::where('active', true)->get();
        $agents = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $statusOptions = [
            ['id' => 'pending', 'name' => 'Pending'],
            ['id' => 'quotation_ready', 'name' => 'Quotation Ready'],
            ['id' => 'order_placed', 'name' => 'Order Placed'],
        ];

        return Inertia::render('Quotation/List', compact('data', 'permissions', 'counties', 'agents', 'statusOptions', 'county_id', 'agent_id', 'status', 'isAdmin'));
    }

    public function create(Request $request)
    {
        $counties = County::where('active', true)->get();
        $lead = null;

        if ($request->has('lead_id')) {
            $lead = Lead::findOrFail($request->lead_id);
        }

        return Inertia::render('Quotation/Add', compact('counties', 'lead'));
    }

    public function store(StoreQuotationRequest $request)
    {
        $data = $request->validated();
        DB::beginTransaction();
        try {

            if(isset($data['id'])){
                // Update existing quotation
                $data['updated_by'] = auth()->id();
                $data['updated_at'] = now();

                $quotation = Quotation::find($data['id']);

                $priceQtyFields = [
                    $data['price_qty_1'],
                    $data['price_qty_2'],
                    $data['price_qty_3'],
                    $data['price_qty_4'],
                ];

                $hasValue = collect($priceQtyFields)->contains(function ($value) {
                    return !is_null($value) && $value != '';
                });

                if ($hasValue && $quotation->status !== 'quotation_ready') {
                    $data['status'] = 'quotation_ready';
                    notificationService::createQuotationCreated($quotation);
                     $task = Task::create([
                        'title' => 'Follow-up: '.$quotation->lead->client_name,
                        'description' => 'Follow-up task for quotation',
                        'type' => 'follow_up',
                        'priority' => 'medium',
                        'due_date' => now()->addDays(2),
                        'assigned_to' => $quotation->lead->created_by,
                        'created_by' => auth()->id(),
                        'lead_id' => $quotation->lead_id,
                    ]);
                    NotificationService::createFollowUpReminder($quotation->lead, $task);

                }

                $quotation->update($data);
                DB::commit();
                return Redirect::to('/quotations')->with('success', 'Quotation updated Successfully');
            } else {
                // Create new quotation
                $year = date('Y');
                $lastQuotation = Quotation::whereYear('created_at', $year)->latest()->first();
                $lastId = $lastQuotation ? intval(explode('-', $lastQuotation->quotation_number)[1]) : 0;
                $quotationNumber = 'QUO-' . $year . '-' . str_pad($lastId + 1, 4, '0', STR_PAD_LEFT);
                $data['quotation_number'] = $quotationNumber;
                $data['created_by'] = $data['updated_by'] = auth()->id();

                // Set default valid until date (30 days from now)
                // if (!isset($data['valid_until'])) {
                //     $data['valid_until'] = now()->addDays(30)->format('Y-m-d');
                // }

                $quotation = Quotation::create($data);

                // If created from lead, update lead status to quotation
                if (isset($data['lead_id']) && $data['lead_id']) {
                    Lead::find($data['lead_id'])->update(['status' => 'quotation']);
                }

                DB::commit();
                return Redirect::to('/quotations')->with('success', 'Quotation created Successfully');
            }

        } catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());
            return Redirect::to('/quotations')->with('error', $e->getMessage());
        }
    }

    public function show(string $id)
    {
        $quotation = Quotation::with(['lead.county', 'creator', 'documents'])->findOrFail($id);

        if (!auth()->user()->hasRole('Admin') && $quotation->created_by !== auth()->id()) {
            return Redirect::to('/quotations')->with('error', 'You are not authorized to view this quotation');
        }

        return Inertia::render('Quotation/Show', compact('quotation'));
    }

    public function edit(string $id)
    {
        $data = Quotation::with(['documents', 'lead.county'])->findOrFail($id);
        $filepath = Config::get('constants.uploadFilePath.quotationDocument');
        $counties = County::where('active', true)->get();
        $permissions = [
            'canEditPrice' => auth()->user()->hasRole('Admin'),
        ];
        return Inertia::render('Quotation/Edit', compact('data', 'counties', 'filepath', 'permissions'));
    }

    public function destroy(string $id)
    {
        $quotation = Quotation::findOrFail($id);

        if (!auth()->user()->hasRole('Admin') && $quotation->created_by !== auth()->id()) {
            return Redirect::to('/quotations')->with('error', 'You are not authorized to delete this quotation');
        }

        DB::beginTransaction();
        try {
            $quotation->delete();
            DB::commit();
            return Redirect::to('/quotations')->with('success', 'Quotation Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/quotations')->with('error', $e->getMessage());
        }
    }

    public function generatePdf(string $id)
    {
        $quotation = Quotation::with(['county', 'creator', 'lead'])->findOrFail($id);
        $currencySymbolMap = [
            'United Kingdom' => 'GBP £',
            'United States'  => 'USD $',
            'Canada'         => 'CAD $',
            'Australia'      => 'AUD $',
        ];
        $matchedKey = collect(array_keys($currencySymbolMap))
            ->first(fn($key) => stripos($quotation, $key) !== false, 'United Kingdom');

        $currencySymbol = $currencySymbolMap[$matchedKey] ?? '£';
        $pdf = PDF::loadView('quotations.pdf', compact('quotation', 'currencySymbol'));
        return $pdf->download('quotation-' . $quotation->quotation_number . '.pdf');
    }

    public function updateStatus(Request $request, string $id)
    {
        $request->validate([
            'status' => 'required|in:draft,sent,accepted,rejected,expired'
        ]);

        $quotation = Quotation::findOrFail($id);

        DB::beginTransaction();
        try {
            $quotation->update([
                'status' => $request->status,
                'updated_by' => auth()->id()
            ]);
            DB::commit();
            return Redirect::back()->with('success', 'Quotation status updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function removedocument($id)
    {
        DB::beginTransaction();
        try {
            $document = Document::find($id);
            if ($document) {
                $filePath = Config::get('constants.uploadFilePath.quotationDocument');
                $filePathToDelete = $filePath['default']. $document->name;
                if (file_exists($filePathToDelete)) {
                    unlink($filePathToDelete); // Delete the file
                }
                $document->delete();
            }
            DB::commit();
            return Redirect::back()->with('success','Document Removed Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function convertFromLead(string $leadId)
    {
        $lead = Lead::with('county')->findOrFail($leadId);
        $counties = County::where('active', true)->get();
        return Inertia::render('Quotation/Add', compact('counties', 'lead'));
    }
}
