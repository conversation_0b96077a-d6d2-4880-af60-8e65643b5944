<script setup>
import { ref, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import { Head } from '@inertiajs/vue3';

const props = defineProps({
    orders: {
        type: Object,
        required: true
    },
    stats: {
        type: Object,
        required: true
    }
});

const getStatusClass = (status) => {
    const classes = {
        'pending': 'bg-yellow-100 text-yellow-800',
        'confirmed': 'bg-blue-100 text-blue-800',
        'under_production': 'bg-purple-100 text-purple-800',
        'shipped': 'bg-indigo-100 text-indigo-800',
        'delivered': 'bg-green-100 text-green-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
};

const getStatusDisplayName = (status) => {
    const names = {
        'pending': 'Pending',
        'confirmed': 'Confirmed',
        'under_production': 'Under Production',
        'shipped': 'Shipped',
        'delivered': 'Delivered'
    };
    return names[status] || status;
};

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-GB', {
        style: 'currency',
        currency: 'GBP'
    }).format(amount);
};

const formatDate = (date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    });
};

// Group orders by status for dashboard view
const ordersByStatus = computed(() => {
    const grouped = {
        pending: [],
        confirmed: [],
        under_production: [],
        shipped: [],
        delivered: []
    };

    props.orders.data.forEach(order => {
        if (grouped[order.status]) {
            grouped[order.status].push(order);
        }
    });

    return grouped;
});
</script>

<template>
    <Head title="Orders Dashboard" />

    <AdminLayout>
        <div class="animate-top">
            <!-- Header -->
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Orders Dashboard</h1>
                    <p class="text-gray-600 mt-1">Overview of all orders and their statuses</p>
                </div>
                <div class="flex space-x-3">
                    <ActionLink :href="route('orders.index')">
                        <template #svg>
                            <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 17.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                                </svg>
                                View All Orders
                            </button>
                        </template>
                    </ActionLink>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Pending</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ stats.pending || 0 }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Confirmed</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ stats.confirmed || 0 }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">In Production</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ stats.under_production || 0 }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"></path>
                                        <path d="M3 4a1 1 0 00-1 1v1a1 1 0 001 1h1a1 1 0 001-1V5a1 1 0 00-1-1H3zM3 10a1 1 0 00-1 1v1a1 1 0 001 1h1a1 1 0 001-1v-1a1 1 0 00-1-1H3zM3 16a1 1 0 00-1 1v1a1 1 0 001 1h1a1 1 0 001-1v-1a1 1 0 00-1-1H3zM7 4a1 1 0 00-1 1v1a1 1 0 001 1h10a1 1 0 001-1V5a1 1 0 00-1-1H7zM7 10a1 1 0 00-1 1v1a1 1 0 001 1h10a1 1 0 001-1v-1a1 1 0 00-1-1H7zM7 16a1 1 0 00-1 1v1a1 1 0 001 1h10a1 1 0 001-1v-1a1 1 0 00-1-1H7z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Shipped</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ stats.shipped || 0 }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Delivered</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ stats.delivered || 0 }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Orders by Status -->
            <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                <!-- Confirmed Orders -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                            Confirmed Orders
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ ordersByStatus.confirmed.length }}
                            </span>
                        </h3>
                        <div class="space-y-3 max-h-96 overflow-y-auto">
                            <div v-for="order in ordersByStatus.confirmed" :key="order.id" class="border border-gray-200 rounded-lg p-3 hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-blue-600">{{ order.order_number }}</p>
                                        <p class="text-sm text-gray-600">{{ order.client_name }}</p>
                                        <p class="text-xs text-gray-500">{{ formatDate(order.expected_delivery) }}</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-semibold text-green-600">{{ formatCurrency(order.total_amount) }}</p>
                                        <ActionLink :href="route('orders.show', order.id)">
                                            <template #svg>
                                                <button class="text-xs text-blue-600 hover:text-blue-800">View</button>
                                            </template>
                                        </ActionLink>
                                    </div>
                                </div>
                            </div>
                            <div v-if="ordersByStatus.confirmed.length === 0" class="text-center py-4 text-gray-500 text-sm">
                                No confirmed orders
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Under Production -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                            Under Production
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                {{ ordersByStatus.under_production.length }}
                            </span>
                        </h3>
                        <div class="space-y-3 max-h-96 overflow-y-auto">
                            <div v-for="order in ordersByStatus.under_production" :key="order.id" class="border border-gray-200 rounded-lg p-3 hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-blue-600">{{ order.order_number }}</p>
                                        <p class="text-sm text-gray-600">{{ order.client_name }}</p>
                                        <p class="text-xs text-gray-500">{{ formatDate(order.expected_delivery) }}</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-semibold text-green-600">{{ formatCurrency(order.total_amount) }}</p>
                                        <ActionLink :href="route('orders.show', order.id)">
                                            <template #svg>
                                                <button class="text-xs text-blue-600 hover:text-blue-800">View</button>
                                            </template>
                                        </ActionLink>
                                    </div>
                                </div>
                            </div>
                            <div v-if="ordersByStatus.under_production.length === 0" class="text-center py-4 text-gray-500 text-sm">
                                No orders in production
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shipped Orders -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                            Shipped Orders
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                {{ ordersByStatus.shipped.length }}
                            </span>
                        </h3>
                        <div class="space-y-3 max-h-96 overflow-y-auto">
                            <div v-for="order in ordersByStatus.shipped" :key="order.id" class="border border-gray-200 rounded-lg p-3 hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-blue-600">{{ order.order_number }}</p>
                                        <p class="text-sm text-gray-600">{{ order.client_name }}</p>
                                        <p v-if="order.tracking_number" class="text-xs text-blue-600 font-mono">{{ order.tracking_number }}</p>
                                        <p class="text-xs text-gray-500">{{ formatDate(order.expected_delivery) }}</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-semibold text-green-600">{{ formatCurrency(order.total_amount) }}</p>
                                        <ActionLink :href="route('orders.show', order.id)">
                                            <template #svg>
                                                <button class="text-xs text-blue-600 hover:text-blue-800">View</button>
                                            </template>
                                        </ActionLink>
                                    </div>
                                </div>
                            </div>
                            <div v-if="ordersByStatus.shipped.length === 0" class="text-center py-4 text-gray-500 text-sm">
                                No shipped orders
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
