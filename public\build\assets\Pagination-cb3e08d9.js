import{T as _,r as c,b as a,d as n,h as x,F as h,k as b,e as m,u as v,y,n as w,j as k}from"./app-1bd09312.js";function D(o,g={}){const l=_({}),e=c(""),t=c("id"),r=c("desc"),u=c({...g}),p=s=>{u.value={...u.value,...s}},d=(s={})=>{const i={...u.value,...s,search:e.value,sort_by:t.value,sort_direction:r.value},f=new URLSearchParams(window.location.search).get("page");f&&(i.page=f),l.get(route(o,i),{preserveState:!0,replace:!0})};return{form:l,search:e,sort:(s,i=!0)=>{i&&(t.value===s?r.value=r.value==="asc"?"desc":"asc":(r.value="asc",t.value=s),d())},fetchData:d,sortKey:t,sortDirection:r,updateParams:p}}const L={key:0},P={class:"flex flex-wrap justify-end isolate rounded-md"},T={key:0},H={key:1},N={__name:"Pagination",props:["links"],setup(o){return(g,l)=>o.links.length>1?(a(),n("div",L,[x("div",P,[(a(!0),n(h,null,b(o.links,(e,t)=>(a(),n(h,{key:t},[e.url===null?(a(),n("div",T,[m(v(y),{innerHTML:e.label,href:"#",class:"inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 bg-white hover:bg-gray-50 focus:z-20 focus:outline-offset-0"},null,8,["innerHTML"])])):(a(),n("div",H,[m(v(y),{innerHTML:e.label,href:e.url,class:w([{"bg-indigo-600 text-white hover:bg-indigo-600":e.active},"bg-white inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"])},null,8,["innerHTML","href","class"])]))],64))),128))])])):k("",!0)}};export{N as _,D as s};
