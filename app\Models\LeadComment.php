<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LeadComment extends Model
{
    use HasFactory, ActivityTrait;

    protected $table = 'lead_comments';

    protected static $logName = 'Comments';

    public function getLogDescription(string $event): string
    {
        return "Comment has been {$event} by {$this->user->first_name} {$this->user->last_name}";
    }

    protected static $logAttributes = [
        'lead_id',
        'user_id',
        'comment',
        'type',
        'is_edited',
        'edited_at'
    ];

    protected $fillable = [
        'lead_id',
        'user_id',
        'comment',
        'type',
        'is_edited',
        'edited_at'
    ];

    protected $casts = [
        'won_amount' => 'decimal:2',
        'is_edited' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'edited_at' => 'datetime',
    ];

    public function lead(): BelongsTo
    {
        return $this->belongsTo(Lead::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Helper methods
    public function isStatusChange(): bool
    {
        return $this->type === 'status_change';
    }

    public function isLostReason(): bool
    {
        return $this->type === 'lost_reason';
    }

    public function isGeneral(): bool
    {
        return $this->type === 'general';
    }

    public function canEdit($userId): bool
    {
        return $this->user_id === $userId || auth()->user()->role_id === 1; // Admin can edit all
    }

    public function canDelete($userId): bool
    {
        return $this->user_id === $userId || auth()->user()->role_id === 1; // Admin can delete all
    }

    public function getFormattedType(): string
    {
        return match($this->type) {
            'status_change' => 'Status Change',
            'lost_reason' => 'Lost Reason',
            'general' => 'Comment',
            default => 'Comment'
        };
    }
}
