<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('tasks', function (Blueprint $table) {
            // Add lead_id column
            $table->unsignedBigInteger('lead_id')->nullable()->after('created_by');

            // Add foreign key constraint
            $table->foreign('lead_id')->references('id')->on('leads')->onDelete('set null');

            // Remove taskable columns if they exist
            if (Schema::hasColumn('tasks', 'taskable_type')) {
                $table->dropColumn('taskable_type');
            }
            if (Schema::hasColumn('tasks', 'taskable_id')) {
                $table->dropColumn('taskable_id');
            }

            // Remove unnecessary columns if they exist
            if (Schema::hasColumn('tasks', 'metadata')) {
                $table->dropColumn('metadata');
            }
            if (Schema::hasColumn('tasks', 'is_recurring')) {
                $table->dropColumn('is_recurring');
            }
            if (Schema::hasColumn('tasks', 'recurring_pattern')) {
                $table->dropColumn('recurring_pattern');
            }
        });
    }

    public function down()
    {
        Schema::table('tasks', function (Blueprint $table) {
            // Drop foreign key and column
            $table->dropForeign(['lead_id']);
            $table->dropColumn('lead_id');

            // Add back taskable columns
            $table->string('taskable_type')->nullable();
            $table->unsignedBigInteger('taskable_id')->nullable();
        });
    }
};
