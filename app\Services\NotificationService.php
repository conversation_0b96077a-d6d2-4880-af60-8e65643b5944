<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\Task;
use App\Models\Lead;
use App\Models\Quotation;
use App\Models\Order;
use App\Models\User;

class NotificationService
{
    /**
     * Create a task reminder notification
     */
    public static function createTaskReminder(Task $task): Notification
    {
        return Notification::create([
            'user_id' => $task->assigned_to,
            'title' => 'Task Reminder',
            'message' => "Task '{$task->title}' is due soon",
            'type' => 'task_reminder',
            'priority' => $task->priority,
            'notifiable_type' => Task::class,
            'notifiable_id' => $task->id,
            'action_data' => [
                'url' => route('tasks.show', $task->id),
                'action' => 'View Task'
            ]
        ]);
    }

    /**
     * Create a task overdue notification
     */
    public static function createTaskOverdue(Task $task): Notification
    {
        return Notification::create([
            'user_id' => $task->assigned_to,
            'title' => 'Task Overdue',
            'message' => "Task '{$task->title}' is overdue",
            'type' => 'task_overdue',
            'priority' => 'urgent',
            'notifiable_type' => Task::class,
            'notifiable_id' => $task->id,
            'action_data' => [
                'url' => route('tasks.show', $task->id),
                'action' => 'View Task'
            ]
        ]);
    }

    /**
     * Create a task assignment notification
     */
    public static function createTaskAssigned(Task $task): Notification
    {
        return Notification::create([
            'user_id' => $task->assigned_to,
            'title' => 'New Task Assigned',
            'message' => "You have been assigned a new task: '{$task->title}'",
            'type' => 'task_reminder',
            'priority' => $task->priority,
            'notifiable_type' => Task::class,
            'notifiable_id' => $task->id,
            'action_data' => [
                'url' => route('tasks.show', $task->id),
                'action' => 'View Task'
            ]
        ]);
    }

    /**
     * Create a lead status update notification
     */
    public static function createLeadUpdate(Lead $lead, string $oldStatus, string $newStatus, int $userId): Notification
    {
        return Notification::create([
            'user_id' => $userId,
            'title' => 'Lead Status Updated',
            'message' => "Lead '{$lead->client_name}' status changed from {$oldStatus} to {$newStatus}",
            'type' => 'lead_update',
            'priority' => 'medium',
            'notifiable_type' => Lead::class,
            'notifiable_id' => $lead->id,
            'action_data' => [
                'url' => route('leads.show', $lead->id),
                'action' => 'View Lead'
            ]
        ]);
    }

    /**
     * Create a quotation created notification
     */
    public static function createQuotationCreated(Quotation $quotation): Notification
    {
        return Notification::create([
            'user_id' => $quotation->created_by,
            'title' => 'Quotation Created',
            'message' => "Quotation #{$quotation->quotation_number} has been created for {$quotation->lead->client_name}",
            'type' => 'quotation_update',
            'priority' => 'medium',
            'notifiable_type' => Quotation::class,
            'notifiable_id' => $quotation->id,
            'action_data' => [
                'url' => route('quotations.show', $quotation->id),
                'action' => 'View Quotation'
            ]
        ]);
    }

    /**
     * Create an order confirmed notification
     */
    public static function createOrderConfirmed(Order $order): Notification
    {
        return Notification::create([
            'user_id' => 1,
            'title' => 'Order Confirmed',
            'message' => "Order #{$order->order_number} has been confirmed for {$order->lead->client_name}",
            'type' => 'order_update',
            'priority' => 'high',
            'notifiable_type' => Order::class,
            'notifiable_id' => $order->id,
            'action_data' => [
                'url' => route('orders.show', $order->id),
                'action' => 'View Order'
            ]
        ]);
    }

    /**
     * Create follow-up reminder notification
     */
    public static function createFollowUpReminder(Lead $lead, Task $task): Notification
    {
        return Notification::create([
            'user_id' => $lead->created_by,
            'title' => 'Follow-up',
            'message' => "Time to follow up with {$lead->client_name}",
            'type' => 'follow_up_due',
            'priority' => 'medium',
            'notifiable_type' => Task::class,
            'notifiable_id' => $task->id,
            'action_data' => [
                'url' => route('tasks.show', $task->id),
                'action' => 'View Lead'
            ]
        ]);
    }

    /**
     * Create call scheduled notification
     */
    public static function createCallScheduled(Task $task): Notification
    {
        return Notification::create([
            'user_id' => $task->assigned_to,
            'title' => 'Call Scheduled',
            'message' => "Call scheduled: {$task->title}",
            'type' => 'call_scheduled',
            'priority' => $task->priority,
            'notifiable_type' => Task::class,
            'notifiable_id' => $task->id,
            'scheduled_for' => $task->reminder_date,
            'action_data' => [
                'url' => route('tasks.show', $task->id),
                'action' => 'View Task'
            ]
        ]);
    }

    /**
     * Notify all admins about important events
     */
    public static function notifyAdmins(string $title, string $message, string $type = 'system', string $priority = 'medium', $relatedEntity = null): void
    {
        $admins = User::where('role_id', 1)->get(); // Assuming role_id 1 is admin

        foreach ($admins as $admin) {
            Notification::create([
                'user_id' => $admin->id,
                'title' => $title,
                'message' => $message,
                'type' => $type,
                'priority' => $priority,
                'notifiable_type' => $relatedEntity ? get_class($relatedEntity) : null,
                'notifiable_id' => $relatedEntity ? $relatedEntity->id : null,
            ]);
        }
    }

    /**
     * Create bulk notifications for multiple users
     */
    public static function createBulkNotifications(array $userIds, array $notificationData): void
    {
        foreach ($userIds as $userId) {
            Notification::create([
                'user_id' => $userId,
                ...$notificationData
            ]);
        }
    }

    /**
     * Schedule a notification for later
     */
    public static function scheduleNotification(int $userId, array $notificationData, \DateTime $scheduledFor): Notification
    {
        return Notification::create([
            'user_id' => $userId,
            'scheduled_for' => $scheduledFor,
            ...$notificationData
        ]);
    }

    /**
     * Get unread notification count for a user
     */
    public static function getUnreadCount(int $userId): int
    {
        return Notification::where('user_id', $userId)
            ->where('is_read', false)
            ->count();
    }

    /**
     * Mark all notifications as read for a user
     */
    public static function markAllAsRead(int $userId): void
    {
        Notification::where('user_id', $userId)
            ->where('is_read', false)
            ->update([
                'is_read' => true,
                'read_at' => now()
            ]);
    }
}
