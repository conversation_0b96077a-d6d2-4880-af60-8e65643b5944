import{_ as p,a as h}from"./AdminLayout-0309e1ff.js";import{c as y,b as d,d as l,e as a,u as b,f as c,F as u,Z as w,h as e,t as s,l as m,k as g,j as _}from"./app-1bd09312.js";import"./_plugin-vue_export-helper-c27b6911.js";const k={class:"animate-top"},z={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},M=e("div",null,[e("h1",{class:"text-3xl font-bold text-gray-900"},"Orders Dashboard"),e("p",{class:"text-gray-600 mt-1"},"Overview of all orders and their statuses")],-1),j={class:"flex space-x-3"},V=e("button",{class:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 17.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z"})]),m(" View All Orders ")],-1),B={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8"},C={class:"bg-white overflow-hidden shadow rounded-lg"},H={class:"p-5"},N={class:"flex items-center"},D=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-yellow-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})])])],-1),O={class:"ml-5 w-0 flex-1"},S=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Pending",-1),L={class:"text-lg font-medium text-gray-900"},P={class:"bg-white overflow-hidden shadow rounded-lg"},F={class:"p-5"},q={class:"flex items-center"},A=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"})])])],-1),E={class:"ml-5 w-0 flex-1"},G=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Confirmed",-1),I={class:"text-lg font-medium text-gray-900"},U={class:"bg-white overflow-hidden shadow rounded-lg"},$={class:"p-5"},T={class:"flex items-center"},Z=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-purple-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z","clip-rule":"evenodd"})])])],-1),J={class:"ml-5 w-0 flex-1"},K=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"In Production",-1),Q={class:"text-lg font-medium text-gray-900"},R={class:"bg-white overflow-hidden shadow rounded-lg"},W={class:"p-5"},X={class:"flex items-center"},Y=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-indigo-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"}),e("path",{d:"M3 4a1 1 0 00-1 1v1a1 1 0 001 1h1a1 1 0 001-1V5a1 1 0 00-1-1H3zM3 10a1 1 0 00-1 1v1a1 1 0 001 1h1a1 1 0 001-1v-1a1 1 0 00-1-1H3zM3 16a1 1 0 00-1 1v1a1 1 0 001 1h1a1 1 0 001-1v-1a1 1 0 00-1-1H3zM7 4a1 1 0 00-1 1v1a1 1 0 001 1h10a1 1 0 001-1V5a1 1 0 00-1-1H7zM7 10a1 1 0 00-1 1v1a1 1 0 001 1h10a1 1 0 001-1v-1a1 1 0 00-1-1H7zM7 16a1 1 0 00-1 1v1a1 1 0 001 1h10a1 1 0 001-1v-1a1 1 0 00-1-1H7z"})])])],-1),ee={class:"ml-5 w-0 flex-1"},te=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Shipped",-1),se={class:"text-lg font-medium text-gray-900"},oe={class:"bg-white overflow-hidden shadow rounded-lg"},de={class:"p-5"},le={class:"flex items-center"},ie=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])])],-1),ne={class:"ml-5 w-0 flex-1"},ae=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Delivered",-1),ce={class:"text-lg font-medium text-gray-900"},re={class:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"},he={class:"bg-white shadow rounded-lg"},ue={class:"px-4 py-5 sm:p-6"},_e={class:"text-lg leading-6 font-medium text-gray-900 mb-4"},me={class:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},xe={class:"space-y-3 max-h-96 overflow-y-auto"},ve={class:"flex items-center justify-between"},ge={class:"text-sm font-medium text-blue-600"},fe={class:"text-sm text-gray-600"},pe={class:"text-xs text-gray-500"},ye={class:"text-right"},be={class:"text-sm font-semibold text-green-600"},we=e("button",{class:"text-xs text-blue-600 hover:text-blue-800"},"View",-1),ke={key:0,class:"text-center py-4 text-gray-500 text-sm"},ze={class:"bg-white shadow rounded-lg"},Me={class:"px-4 py-5 sm:p-6"},je={class:"text-lg leading-6 font-medium text-gray-900 mb-4"},Ve={class:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"},Be={class:"space-y-3 max-h-96 overflow-y-auto"},Ce={class:"flex items-center justify-between"},He={class:"text-sm font-medium text-blue-600"},Ne={class:"text-sm text-gray-600"},De={class:"text-xs text-gray-500"},Oe={class:"text-right"},Se={class:"text-sm font-semibold text-green-600"},Le=e("button",{class:"text-xs text-blue-600 hover:text-blue-800"},"View",-1),Pe={key:0,class:"text-center py-4 text-gray-500 text-sm"},Fe={class:"bg-white shadow rounded-lg"},qe={class:"px-4 py-5 sm:p-6"},Ae={class:"text-lg leading-6 font-medium text-gray-900 mb-4"},Ee={class:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"},Ge={class:"space-y-3 max-h-96 overflow-y-auto"},Ie={class:"flex items-center justify-between"},Ue={class:"text-sm font-medium text-blue-600"},$e={class:"text-sm text-gray-600"},Te={key:0,class:"text-xs text-blue-600 font-mono"},Ze={class:"text-xs text-gray-500"},Je={class:"text-right"},Ke={class:"text-sm font-semibold text-green-600"},Qe=e("button",{class:"text-xs text-blue-600 hover:text-blue-800"},"View",-1),Re={key:0,class:"text-center py-4 text-gray-500 text-sm"},et={__name:"Dashboard",props:{orders:{type:Object,required:!0},stats:{type:Object,required:!0}},setup(n){const f=n,x=o=>new Intl.NumberFormat("en-GB",{style:"currency",currency:"GBP"}).format(o),v=o=>o?new Date(o).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}):"N/A",i=y(()=>{const o={pending:[],confirmed:[],under_production:[],shipped:[],delivered:[]};return f.orders.data.forEach(r=>{o[r.status]&&o[r.status].push(r)}),o});return(o,r)=>(d(),l(u,null,[a(b(w),{title:"Orders Dashboard"}),a(p,null,{default:c(()=>[e("div",k,[e("div",z,[M,e("div",j,[a(h,{href:o.route("orders.index")},{svg:c(()=>[V]),_:1},8,["href"])])]),e("div",B,[e("div",C,[e("div",H,[e("div",N,[D,e("div",O,[e("dl",null,[S,e("dd",L,s(n.stats.pending||0),1)])])])])]),e("div",P,[e("div",F,[e("div",q,[A,e("div",E,[e("dl",null,[G,e("dd",I,s(n.stats.confirmed||0),1)])])])])]),e("div",U,[e("div",$,[e("div",T,[Z,e("div",J,[e("dl",null,[K,e("dd",Q,s(n.stats.under_production||0),1)])])])])]),e("div",R,[e("div",W,[e("div",X,[Y,e("div",ee,[e("dl",null,[te,e("dd",se,s(n.stats.shipped||0),1)])])])])]),e("div",oe,[e("div",de,[e("div",le,[ie,e("div",ne,[e("dl",null,[ae,e("dd",ce,s(n.stats.delivered||0),1)])])])])])]),e("div",re,[e("div",he,[e("div",ue,[e("h3",_e,[m(" Confirmed Orders "),e("span",me,s(i.value.confirmed.length),1)]),e("div",xe,[(d(!0),l(u,null,g(i.value.confirmed,t=>(d(),l("div",{key:t.id,class:"border border-gray-200 rounded-lg p-3 hover:bg-gray-50"},[e("div",ve,[e("div",null,[e("p",ge,s(t.order_number),1),e("p",fe,s(t.client_name),1),e("p",pe,s(v(t.expected_delivery)),1)]),e("div",ye,[e("p",be,s(x(t.total_amount)),1),a(h,{href:o.route("orders.show",t.id)},{svg:c(()=>[we]),_:2},1032,["href"])])])]))),128)),i.value.confirmed.length===0?(d(),l("div",ke," No confirmed orders ")):_("",!0)])])]),e("div",ze,[e("div",Me,[e("h3",je,[m(" Under Production "),e("span",Ve,s(i.value.under_production.length),1)]),e("div",Be,[(d(!0),l(u,null,g(i.value.under_production,t=>(d(),l("div",{key:t.id,class:"border border-gray-200 rounded-lg p-3 hover:bg-gray-50"},[e("div",Ce,[e("div",null,[e("p",He,s(t.order_number),1),e("p",Ne,s(t.client_name),1),e("p",De,s(v(t.expected_delivery)),1)]),e("div",Oe,[e("p",Se,s(x(t.total_amount)),1),a(h,{href:o.route("orders.show",t.id)},{svg:c(()=>[Le]),_:2},1032,["href"])])])]))),128)),i.value.under_production.length===0?(d(),l("div",Pe," No orders in production ")):_("",!0)])])]),e("div",Fe,[e("div",qe,[e("h3",Ae,[m(" Shipped Orders "),e("span",Ee,s(i.value.shipped.length),1)]),e("div",Ge,[(d(!0),l(u,null,g(i.value.shipped,t=>(d(),l("div",{key:t.id,class:"border border-gray-200 rounded-lg p-3 hover:bg-gray-50"},[e("div",Ie,[e("div",null,[e("p",Ue,s(t.order_number),1),e("p",$e,s(t.client_name),1),t.tracking_number?(d(),l("p",Te,s(t.tracking_number),1)):_("",!0),e("p",Ze,s(v(t.expected_delivery)),1)]),e("div",Je,[e("p",Ke,s(x(t.total_amount)),1),a(h,{href:o.route("orders.show",t.id)},{svg:c(()=>[Qe]),_:2},1032,["href"])])])]))),128)),i.value.shipped.length===0?(d(),l("div",Re," No shipped orders ")):_("",!0)])])])])])]),_:1})],64))}};export{et as default};
