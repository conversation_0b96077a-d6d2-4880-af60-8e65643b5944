<template>
    <Head title="Tasks Dashboard" />

    <AdminLayout>
        <div class="animate-top space-y-6">
            <!-- Header -->
            <div class="bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-2xl font-semibold leading-7 text-gray-900">Tasks Dashboard</h2>
                        <p class="text-sm text-gray-600 mt-1">Overview of your tasks and productivity</p>
                    </div>
                    <div class="flex space-x-2">
                        <Link :href="route('tasks.index')"
                                class="px-4 py-2 bg-slate-100 border border-transparent rounded-md text-sm font-semibold leading-6 text-gray-900 hover:bg-gray-100">
                            ← Back to Tasks
                        </Link>
                    </div>
                </div>
            </div>

            <!-- Stats Overview -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div class="bg-white border rounded-lg p-6 shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Tasks</p>
                            <p class="text-2xl font-bold text-gray-900">{{ stats.total_tasks }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white border rounded-lg p-6 shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Pending</p>
                            <p class="text-2xl font-bold text-yellow-600">{{ stats.pending_tasks }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white border rounded-lg p-6 shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Overdue</p>
                            <p class="text-2xl font-bold text-red-600">{{ stats.overdue_tasks }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white border rounded-lg p-6 shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Due Today</p>
                            <p class="text-2xl font-bold text-green-600">{{ stats.due_today }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white border rounded-lg p-6 shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">This Week</p>
                            <p class="text-2xl font-bold text-purple-600">{{ stats.completed_this_week }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Recent Tasks -->
                <div class="bg-white border rounded-lg p-6 shadow-sm">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Recent Tasks</h3>
                        <Link :href="route('tasks.index')" class="text-sm text-blue-600 hover:text-blue-800">
                            View All →
                        </Link>
                    </div>
                    <div class="space-y-3">
                        <div v-for="task in recentTasks" :key="task.id"
                             class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                          :class="getPriorityColor(task.priority)">
                                        {{ task.priority }}
                                    </span>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                          :class="getTypeColor(task.type)">
                                        {{ formatType(task.type) }}
                                    </span>
                                </div>
                                <h4 class="text-sm font-medium text-gray-900 mt-1">{{ task.title }}</h4>
                                <p class="text-xs text-gray-700">
                                    Assigned to: {{ task.assigned_to.first_name }} {{ task.assigned_to.last_name }}
                                </p>
                            </div>
                            <div class="text-right">
                                <div class="text-xs text-gray-700">{{ formatDate(task.created_at) }}</div>
                                <Link :href="route('tasks.show', task.id)"
                                      class="text-xs text-blue-600 hover:text-blue-800">View</Link>
                            </div>
                        </div>
                        <div v-if="recentTasks.length === 0" class="text-center py-4 text-gray-700">
                            No recent tasks
                        </div>
                    </div>
                </div>

                <!-- Upcoming Tasks -->
                <div class="bg-white border rounded-lg p-6 shadow-sm">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Upcoming Tasks</h3>
                        <Link :href="route('tasks.index', { status: 'pending' })" class="text-sm text-blue-600 hover:text-blue-800">
                            View All →
                        </Link>
                    </div>
                    <div class="space-y-3">
                        <div v-for="task in upcomingTasks" :key="task.id"
                             class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                          :class="getPriorityColor(task.priority)">
                                        {{ task.priority }}
                                    </span>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                          :class="getTypeColor(task.type)">
                                        {{ formatType(task.type) }}
                                    </span>
                                </div>
                                <h4 class="text-sm font-medium text-gray-900 mt-1">{{ task.title }}</h4>
                                <p class="text-xs text-gray-700">
                                    Assigned to: {{ task.assigned_to.first_name }} {{ task.assigned_to.last_name }}
                                </p>
                            </div>
                            <div class="text-right">
                                <div class="text-xs"
                                     :class="{ 'text-red-600 font-semibold': isOverdue(task.due_date), 'text-gray-700': !isOverdue(task.due_date) }">
                                    {{ formatDate(task.due_date) }}
                                </div>
                                <Link :href="route('tasks.show', task.id)"
                                      class="text-xs text-blue-600 hover:text-blue-800">View</Link>
                            </div>
                        </div>
                        <div v-if="upcomingTasks.length === 0" class="text-center py-4 text-gray-700">
                            No upcoming tasks
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white border rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <Link :href="route('tasks.create', { type: 'call' })"
                          class="flex flex-col items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-2">
                            <span class="text-2xl">📞</span>
                        </div>
                        <span class="text-sm font-medium text-blue-900">Schedule Call</span>
                    </Link>

                    <Link :href="route('tasks.create', { type: 'follow_up' })"
                          class="flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-2">
                            <span class="text-2xl">🔄</span>
                        </div>
                        <span class="text-sm font-medium text-green-900">Follow Up</span>
                    </Link>

                    <Link :href="route('tasks.create', { type: 'meeting' })"
                          class="flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-2">
                            <span class="text-2xl">🤝</span>
                        </div>
                        <span class="text-sm font-medium text-purple-900">Schedule Meeting</span>
                    </Link>

                    <Link :href="route('tasks.create', { type: 'reminder' })"
                          class="flex flex-col items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-2">
                            <span class="text-2xl">⏰</span>
                        </div>
                        <span class="text-sm font-medium text-yellow-900">Set Reminder</span>
                    </Link>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'

const props = defineProps({
    stats: Object,
    recentTasks: Array,
    upcomingTasks: Array
})


const formatDate = (date) => {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const year = d.getFullYear();
    return `${day}/${month}/${year}`;
};

const formatType = (type) => {
    return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const isOverdue = (dueDate) => {
    return new Date(dueDate) < new Date()
}

const getTypeColor = (type) => {
    const colors = {
        call: 'bg-blue-100 text-blue-800',
        follow_up: 'bg-yellow-100 text-yellow-800',
        meeting: 'bg-purple-100 text-purple-800',
        email: 'bg-green-100 text-green-800',
        quote_follow_up: 'bg-orange-100 text-orange-800',
        order_follow_up: 'bg-red-100 text-red-800',
        general: 'bg-gray-100 text-gray-800',
        reminder: 'bg-pink-100 text-pink-800'
    }
    return colors[type] || 'bg-gray-100 text-gray-800'
}

const getPriorityColor = (priority) => {
    const colors = {
        low: 'bg-green-100 text-green-800',
        medium: 'bg-yellow-100 text-yellow-800',
        high: 'bg-orange-100 text-orange-800',
        urgent: 'bg-red-100 text-red-800'
    }
    return colors[priority] || 'bg-gray-100 text-gray-800'
}
</script>
