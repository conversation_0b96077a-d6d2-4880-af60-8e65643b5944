
<script setup>
import { ref, computed } from 'vue'
import { Head, Link,useForm, router } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import InputLabel from '@/Components/InputLabel.vue';
import CreateButton from '@/Components/CreateButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Modal from '@/Components/Modal.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import axios from 'axios';
import { sortAndSearch } from '@/Composables/sortAndSearch';

const { form  } = sortAndSearch('tasks.index');
const props = defineProps({
    tasks: Object,
    users: Array,
    stats: Object,
    status: Array,
    types: Array,
    priorities: Array,
    permissions: Object
})

const filters = ref({
    status: '',
    type: '',
    priority: '',
    assigned_to: ''
})


const setStatus = (status) => {
    filters.value.status = status;
    applyFilters();
};

const setType = (type) => {
    filters.value.type = type;
    applyFilters();
};

const setPriority = (priority) => {
    filters.value.priority = priority;
    applyFilters();
};

const setAssignedTo = (assigned_to) => {
    filters.value.assigned_to = assigned_to;
    applyFilters();
};

const applyFilters = () => {
    router.get(route('tasks.index'), filters.value, {
        preserveState: true,
        preserveScroll: true
    })
}

const modalVisible = ref(false);
const selectedTaskId = ref(null);

const oncompleteTask = (taskId) => {
    selectedTaskId.value = taskId;
    modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};

const completeTask = () => {
    const taskId = selectedTaskId.value;
    const url = route('tasks.complete', taskId);
    axios.post(url)
    .then(response => {
        console.log("Task completed successfully", response.data.message);
        modalVisible.value = false;
        router.reload();  // Reload page after success
    })
    .catch(error => {
        console.error("Error completing task:", error.response?.data?.error || error.message);
    });
};

const formatDate = (date) => {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const year = d.getFullYear();
    return `${day}/${month}/${year}`;
};

const formatType = (type) => {
    return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatStatus = (status) => {
    return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const isOverdue = (task) => {
    return task.status !== 'completed' && new Date(task.due_date) < new Date();
}

const getTypeColor = (type) => {
    const colors = {
        call: 'bg-blue-100 text-blue-800',
        follow_up: 'bg-yellow-100 text-yellow-800',
        meeting: 'bg-purple-100 text-purple-800',
        email: 'bg-green-100 text-green-800',
        quote_follow_up: 'bg-orange-100 text-orange-800',
        order_follow_up: 'bg-red-100 text-red-800',
        general: 'bg-gray-100 text-gray-800',
        reminder: 'bg-pink-100 text-pink-800'
    }
    return colors[type] || 'bg-gray-100 text-gray-800'
}

const getPriorityColor = (priority) => {
    const colors = {
        low: 'bg-green-100 text-green-800',
        medium: 'bg-yellow-100 text-yellow-800',
        high: 'bg-orange-100 text-orange-800',
        urgent: 'bg-red-100 text-red-800'
    }
    return colors[priority] || 'bg-gray-100 text-gray-800'
}

const getStatusColor = (status) => {
    const colors = {
        pending: 'bg-yellow-100 text-yellow-800',
        in_progress: 'bg-blue-100 text-blue-800',
        completed: 'bg-green-100 text-green-800',
        cancelled: 'bg-red-100 text-red-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
}

const deleteTask = (id) => {
    form.delete(route('tasks.destroy', { task: id }));
};

</script>
<template>
    <Head title="Tasks" />

    <AdminLayout>
        <div class="animate-top">
        <div class="bg-white p-4 shadow sm:p-6 rounded-lg border">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-semibold leading-7 text-gray-900">Tasks</h2>
                    <p class="text-sm text-gray-600 mt-1">Manage your tasks and follow-ups</p>
                </div>
                <div class="flex space-x-2">
                    <Link :href="route('tasks.dashboard')"
                          class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700">
                        📊 Dashboard
                    </Link>
                    <div class="sm:flex-none" v-if="permissions.canCreateTask">
                        <CreateButton :href="route('tasks.create')">
                            Add Task
                        </CreateButton>
                    </div>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-blue-600">Total Tasks</p>
                            <p class="text-2xl font-semibold text-blue-900">{{ stats.total }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-yellow-600">Pending</p>
                            <p class="text-2xl font-semibold text-yellow-900">{{ stats.pending }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-red-600">Overdue</p>
                            <p class="text-2xl font-semibold text-red-900">{{ stats.overdue }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-green-600">Due Today</p>
                            <p class="text-2xl font-semibold text-green-900">{{ stats.due_today }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <InputLabel for="agent_filter" value="Status" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="status"
                            v-model="filters.status"
                            @onchange="setStatus"
                            />
                        </div>
                    </div>
                    <div>
                        <InputLabel for="agent_filter" value="Type" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="types"
                            v-model="filters.type"
                            @onchange="setType"
                            />
                        </div>
                    </div>
                    <div>
                        <InputLabel for="agent_filter" value="Priority" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="priorities"
                            v-model="filters.priority"
                            @onchange="setPriority"
                            />
                        </div>
                    </div>
                    <div>
                        <InputLabel for="agent_filter" value="Assigned To" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="users"
                            v-model="filters.assigned_to"
                            @onchange="setAssignedTo"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <!-- Empty State -->
            <div v-if="tasks.data.length === 0" class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No tasks found</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating a new task.</p>
                <div class="mt-6">
                    <Link :href="route('tasks.create')"
                          class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        New Task
                    </Link>
                </div>
            </div>
        </div>
        <!-- Tasks Cards View (Better for Mobile) -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
            <div v-for="task in tasks.data" :key="task.id" class="bg-white border rounded-lg p-4 hover:shadow-md transition-shadow">
                <!-- Task Header -->
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <h3 class="text-sm font-semibold text-gray-900 mb-1">{{ task.title }}</h3>
                        <p class="text-xs text-gray-500" v-if="task.lead">
                            📋 {{ task.lead.client_name }} ({{ task.lead.company_name }})
                        </p>
                    </div>
                    <div class="flex flex-col items-end space-y-2">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                :class="getPriorityColor(task.priority)">
                            {{ task.priority }}
                        </span>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                :class="getStatusColor(task.status)">
                            {{ formatStatus(task.status) }}
                        </span>
                    </div>
                </div>

                <!-- Task Details -->
                <div class="space-y-2 mb-4">
                    <div class="flex items-center text-sm text-gray-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        {{ task.assigned_to.first_name }} {{ task.assigned_to.last_name }}
                    </div>
                    <div class="flex items-center text-xs"
                            :class="{ 'text-red-600 font-semibold': isOverdue(task), 'text-gray-600': !isOverdue(task) }">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        {{ formatDate(task.due_date) }}
                        <span v-if="isOverdue(task)" class="ml-1 text-red-500">⚠️ Overdue</span>
                    </div>
                    <div class="flex items-center text-xs text-gray-700">
                        <span class="inline-flex px-2 py-1 text-xs  font-semibold rounded-full"
                                :class="getTypeColor(task.type)">
                            {{ formatType(task.type) }}
                        </span>
                    </div>
                </div>

                <!-- Task Actions -->
                <div class="flex space-x-2">
                    <Link :href="route('tasks.show', task.id)"
                        class="flex-1 text-center px-3 py-2 text-xs font-semibold text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100">
                        View
                    </Link>
                    <Link :href="route('tasks.edit', task.id)" v-if="permissions.canEditTask"
                        class="flex-1 text-center px-3 py-2 text-xs font-semibold text-green-600 bg-green-50 rounded-md hover:bg-green-100">
                        Edit
                    </Link>
                    <button v-if="task.status !== 'completed' && permissions.canEditTask"
                        @click="oncompleteTask(task.id)"
                        class="flex-1 px-3 py-2 text-xs font-semibold text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100">
                        Complete
                    </button>
                     <button v-if="permissions.canDeleteTask" @click="deleteTask(task.id)" class="flex-1 px-3 py-2 text-xs font-semibold text-red-600 bg-red-50 rounded-md hover:bg-red-100">
                         Delete
                    </button>
                </div>
            </div>
        </div>

        <Pagination v-if="tasks.links && (tasks.links.length > 0)" class="mt-6" :links="tasks.links"></Pagination>
        </div>
        <Modal :show="modalVisible" @close="closeModal">
            <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to complete this task?
                </h2>
                <div class="mt-6 flex justify-end space-x-4">
                    <SecondaryButton @click="closeModal">Cancel</SecondaryButton>
                    <CreateButton class="w-44" @click="completeTask">
                        Complete Task
                    </CreateButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>
</template>

