{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "require": {"php": "^8.0", "laravel/framework": "^9.0", "barryvdh/laravel-dompdf": "^2.0", "guzzlehttp/guzzle": "^7.2", "inertiajs/inertia-laravel": "^0.6.8", "laravel/breeze": "^1.12", "laravel/sanctum": "^2.15", "laravel/tinker": "^2.8", "maatwebsite/excel": "^3.1", "spatie/laravel-activitylog": "^4.7", "spatie/laravel-permission": "^5.5", "tightenco/ziggy": "^1.0"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.3", "phpunit/phpunit": "^9.5", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Support\\": "app/Support/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "platform": {"php": "8.0.30"}}, "minimum-stability": "stable", "prefer-stable": true}