import{_ as U,a as A}from"./AdminLayout-0309e1ff.js";import{b as d,d as a,e as b,u as O,f,F as B,Z as j,h as t,t as s,j as r,n as I,l as S}from"./app-1bd09312.js";import"./_plugin-vue_export-helper-c27b6911.js";const P={class:"animate-top"},T={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},z={class:"text-3xl font-bold text-gray-900"},K=t("p",{class:"text-gray-600 mt-1"},"Order Details",-1),M={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Q={class:"lg:col-span-2 space-y-8"},V={class:"bg-white shadow rounded-lg p-6"},$=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Lead Information",-1),E={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},F=t("label",{class:"block text-sm font-semibold text-gray-900"},"Client Name",-1),G={class:"mt-1 text-sm text-gray-700"},Z=t("label",{class:"block text-sm font-semibold text-gray-900"},"County",-1),H={class:"mt-1 text-sm text-gray-700"},J=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lead Number",-1),R={class:"mt-1 text-sm text-gray-700"},W=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lead Status",-1),X={class:"mt-1 text-sm text-gray-700"},Y={class:"bg-white shadow rounded-lg p-6"},tt=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Product Specifications",-1),et={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},st=t("label",{class:"block text-sm font-semibold text-gray-900"},"Dimensions",-1),ot={class:"mt-1 text-sm text-gray-700"},dt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Open Size",-1),at={class:"mt-1 text-sm text-gray-700"},rt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Box Style",-1),lt={class:"mt-1 text-sm text-gray-700"},ct=t("label",{class:"block text-sm font-semibold text-gray-900"},"Stock",-1),nt={class:"mt-1 text-sm text-gray-700"},it=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lamination",-1),mt={class:"mt-1 text-sm text-gray-700"},xt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Printing",-1),yt={class:"mt-1 text-sm text-gray-700"},ht={key:0,class:"md:col-span-2"},_t=t("label",{class:"block text-sm font-semibold text-gray-900"},"Add-ons",-1),gt={class:"mt-1 text-sm text-gray-700"},ut={class:"bg-white shadow rounded-lg p-6"},bt=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Order Information",-1),ft={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},wt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Status",-1),pt={key:0},vt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Quotation Number",-1),kt={class:"mt-1 text-sm text-gray-700"},qt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Created By",-1),Nt={class:"mt-1 text-sm text-gray-700"},At={key:1},St=t("label",{class:"block text-sm font-semibold text-gray-900"},"Tracking Number",-1),Ct={class:"mt-1 text-sm text-gray-700"},Lt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Expected Delivery",-1),Dt={class:"mt-1 text-sm text-gray-700"},Ut={key:2},Ot=t("label",{class:"block text-sm font-semibold text-gray-900"},"Actual Delivery",-1),Bt={class:"mt-1 text-sm text-gray-700"},jt={class:"bg-white shadow rounded-lg p-6"},It=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Order Quantities",-1),Pt={class:"overflow-x-auto"},Tt={class:"min-w-full divide-y divide-gray-200"},zt=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Quantity"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Unit Price"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Total")])],-1),Kt={class:"bg-white divide-y divide-gray-200"},Mt={key:0},Qt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Vt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},$t={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},Et={key:1},Ft={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Gt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Zt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},Ht={key:2},Jt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Rt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Wt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},Xt={key:3},Yt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},te={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},ee={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},se={class:"bg-gray-50"},oe=t("td",{colspan:"2",class:"px-6 py-4 text-sm font-semibold text-gray-900 text-right"},"Total Amount:",-1),de={class:"px-6 py-4 text-sm font-bold text-green-600"},ae={key:0,class:"bg-white shadow rounded-lg p-6"},re=t("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Notes",-1),le={class:"text-sm text-gray-700 whitespace-pre-wrap"},ce={class:"space-y-6"},ne={class:"bg-white shadow rounded-lg p-6"},ie=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quick Actions",-1),me={class:"space-y-3 w-full"},xe=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})]),S(" Edit Order ")],-1),ye=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3"})]),S(" Back to Orders ")],-1),he={class:"bg-white shadow rounded-lg p-6"},_e=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Timeline",-1),ge={class:"space-y-4"},ue={class:"flex items-start space-x-3"},be=t("div",{class:"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"},null,-1),fe=t("p",{class:"text-sm font-semibold text-gray-900"},"Order Created",-1),we={class:"text-xs text-gray-500"},pe={key:0,class:"flex items-start space-x-3"},ve=t("div",{class:"flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"},null,-1),ke=t("p",{class:"text-sm font-semibold text-gray-900"},"Order Confirmed",-1),qe={class:"text-xs text-gray-500"},Ne={key:1,class:"flex items-start space-x-3"},Ae=t("div",{class:"flex-shrink-0 w-2 h-2 bg-yellow-500 rounded-full mt-2"},null,-1),Se=t("p",{class:"text-sm font-semibold text-gray-900"},"Last Updated",-1),Ce={class:"text-xs text-gray-500"},Oe={__name:"Show",props:{order:{type:Object,required:!0}},setup(e){const C=e,L=o=>({pending:"bg-yellow-100 text-yellow-800",confirmed:"bg-blue-100 text-blue-800",under_production:"bg-purple-100 text-purple-800",shipped:"bg-indigo-100 text-indigo-800",delivered:"bg-green-100 text-green-800"})[o]||"bg-gray-100 text-gray-800",n=o=>o?new Date(o).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",l=o=>{var m,x;const c={"United Kingdom":{locale:"en-GB",currency:"GBP"},"United States":{locale:"en-US",currency:"USD"},Canada:{locale:"en-CA",currency:"CAD"},Australia:{locale:"en-AU",currency:"AUD"}},y=((x=(m=C.order.lead)==null?void 0:m.county)==null?void 0:x.name)||"UK",h=Object.keys(c).find(u=>y.toLowerCase().includes(u.toLowerCase())),{locale:_,currency:i}=c[h]||c.UK,g=new Intl.NumberFormat(_,{style:"currency",currency:i,currencyDisplay:"symbol"}).format(o);return`${i} ${g}`},D=o=>({pending:"Pending",confirmed:"Confirmed",under_production:"Under Production",shipped:"Shipped",delivered:"Delivered"})[o]||o;return(o,c)=>(d(),a(B,null,[b(O(j),{title:"Orders"}),b(U,null,{default:f(()=>{var y,h,_,i,g,m,x,u,w,p,v,k,q,N;return[t("div",P,[t("div",T,[t("div",null,[t("h1",z,s(e.order.order_number),1),K])]),t("div",M,[t("div",Q,[t("div",V,[$,t("div",E,[t("div",null,[F,t("p",G,s(((y=e.order.lead)==null?void 0:y.client_name)||"N/A"),1)]),t("div",null,[Z,t("p",H,s(((_=(h=e.order.lead)==null?void 0:h.county)==null?void 0:_.name)||"N/A"),1)]),t("div",null,[J,t("p",R,s(((i=e.order.lead)==null?void 0:i.lead_number)||"N/A"),1)]),t("div",null,[W,t("p",X,s(((g=e.order.lead)==null?void 0:g.status)||"N/A"),1)])])]),t("div",Y,[tt,t("div",et,[t("div",null,[st,t("p",ot,s(((m=e.order.lead)==null?void 0:m.dimensions)||"N/A"),1)]),t("div",null,[dt,t("p",at,s(((x=e.order.lead)==null?void 0:x.open_size)||"N/A"),1)]),t("div",null,[rt,t("p",lt,s(((u=e.order.lead)==null?void 0:u.box_style)||"N/A"),1)]),t("div",null,[ct,t("p",nt,s(((w=e.order.lead)==null?void 0:w.stock)||"N/A"),1)]),t("div",null,[it,t("p",mt,s(((p=e.order.lead)==null?void 0:p.lamination)||"N/A"),1)]),t("div",null,[xt,t("p",yt,s(((v=e.order.lead)==null?void 0:v.printing)||"N/A"),1)]),(k=e.order.lead)!=null&&k.add_ons?(d(),a("div",ht,[_t,t("p",gt,s(e.order.lead.add_ons),1)])):r("",!0)])]),t("div",ut,[bt,t("div",ft,[t("div",null,[wt,t("span",{class:I(["inline-flex px-3 py-1 rounded-full text-sm font-semibold mt-1",L(e.order.status)])},s(D(e.order.status)),3)]),e.order.quotation?(d(),a("div",pt,[vt,t("p",kt,s(e.order.quotation.quotation_number),1)])):r("",!0),t("div",null,[qt,t("p",Nt,s((q=e.order.creator)==null?void 0:q.first_name)+" "+s((N=e.order.creator)==null?void 0:N.last_name),1)]),e.order.tracking_number?(d(),a("div",At,[St,t("p",Ct,s(e.order.tracking_number),1)])):r("",!0),t("div",null,[Lt,t("p",Dt,s(n(e.order.expected_delivery)),1)]),e.order.actual_delivery?(d(),a("div",Ut,[Ot,t("p",Bt,s(n(e.order.actual_delivery)),1)])):r("",!0)])]),t("div",jt,[It,t("div",Pt,[t("table",Tt,[zt,t("tbody",Kt,[e.order.selected_qty_1&&e.order.price_qty_1?(d(),a("tr",Mt,[t("td",Qt,s(parseInt(e.order.selected_qty_1).toLocaleString())+" pcs",1),t("td",Vt,s(l(e.order.price_qty_1)),1),t("td",$t,s(l(e.order.selected_qty_1*e.order.price_qty_1)),1)])):r("",!0),e.order.selected_qty_2&&e.order.price_qty_2?(d(),a("tr",Et,[t("td",Ft,s(parseInt(e.order.selected_qty_2).toLocaleString())+" pcs",1),t("td",Gt,s(l(e.order.price_qty_2)),1),t("td",Zt,s(l(e.order.selected_qty_2*e.order.price_qty_2)),1)])):r("",!0),e.order.selected_qty_3&&e.order.price_qty_3?(d(),a("tr",Ht,[t("td",Jt,s(parseInt(e.order.selected_qty_3).toLocaleString())+" pcs",1),t("td",Rt,s(l(e.order.price_qty_3)),1),t("td",Wt,s(l(e.order.selected_qty_3*e.order.price_qty_3)),1)])):r("",!0),e.order.selected_qty_4&&e.order.price_qty_4?(d(),a("tr",Xt,[t("td",Yt,s(parseInt(e.order.selected_qty_4).toLocaleString())+" pcs",1),t("td",te,s(l(e.order.price_qty_4)),1),t("td",ee,s(l(e.order.selected_qty_4*e.order.price_qty_4)),1)])):r("",!0)]),t("tfoot",se,[t("tr",null,[oe,t("td",de,s(l(e.order.total_amount)),1)])])])])]),e.order.notes?(d(),a("div",ae,[re,t("p",le,s(e.order.notes),1)])):r("",!0)]),t("div",ce,[t("div",ne,[ie,t("div",me,[b(A,{href:o.route("orders.edit",e.order.id),class:"w-full"},{svg:f(()=>[xe]),_:1},8,["href"]),b(A,{href:o.route("orders.index"),class:"w-full"},{svg:f(()=>[ye]),_:1},8,["href"])])]),t("div",he,[_e,t("div",ge,[t("div",ue,[be,t("div",null,[fe,t("p",we,s(n(e.order.created_at)),1)])]),e.order.is_confirmed?(d(),a("div",pe,[ve,t("div",null,[ke,t("p",qe,s(n(e.order.confirmed_at)),1)])])):r("",!0),e.order.updated_at!==e.order.created_at?(d(),a("div",Ne,[Ae,t("div",null,[Se,t("p",Ce,s(n(e.order.updated_at)),1)])])):r("",!0)])])])])])]}),_:1})],64))}};export{Oe as default};
