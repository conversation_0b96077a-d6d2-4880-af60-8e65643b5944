<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreQuotationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'nullable|exists:quotations,id',
            'lead_id' => 'nullable|exists:leads,id',
            'price_qty_1' => 'nullable|numeric|min:0',
            'price_qty_2' => 'nullable|numeric|min:0',
            'price_qty_3' => 'nullable|numeric|min:0',
            'price_qty_4' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
            'valid_until' => 'nullable|date|after:today'
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Ensure at least one quantity and price pair is provided
            $hasValidPair = false;

            for ($i = 1; $i <= 4; $i++) {
                $qty = $this->input("qty_{$i}");
                $price = $this->input("price_qty_{$i}");

                if ($qty && $price) {
                    $hasValidPair = true;
                    break;
                }
            }

            if (!$hasValidPair) {
                $validator->errors()->add('qty_1', 'At least one quantity and price pair is required.');
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'client_name.required' => 'Client name is required.',
            'county_id.required' => 'County selection is required.',
            'county_id.exists' => 'Selected county is invalid.',
            'qty_1.required' => 'At least one quantity is required.',
            'price_qty_1.required' => 'Price for quantity 1 is required.',
            'valid_until.after' => 'Valid until date must be in the future.',
        ];
    }
}
