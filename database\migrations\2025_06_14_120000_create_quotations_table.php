<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('quotations', function (Blueprint $table) {
            $table->id();
            $table->string('quotation_number');
            $table->foreignId('lead_id')->constrained('leads'); // Required - references lead for all client data
            $table->decimal('price_qty_1', 10, 2)->nullable();
            $table->decimal('price_qty_2', 10, 2)->nullable();
            $table->decimal('price_qty_3', 10, 2)->nullable();
            $table->decimal('price_qty_4', 10, 2)->nullable();
            $table->text('notes')->nullable();
            $table->enum('status', ['pending', 'quotation_ready', 'order_placed'])->default('pending');
            $table->date('valid_until')->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('quotations');
    }
};
